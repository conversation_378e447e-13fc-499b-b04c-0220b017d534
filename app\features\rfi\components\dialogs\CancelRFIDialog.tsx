import type { ReactNode } from "react";
import type { CancelRFISchema } from "../../schemas/cancel-rfi-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@netpro/design-system";
import { useFetcher, useNavigation, useParams } from "@remix-run/react";
import { Info } from "lucide-react";
import { useEffect, useState } from "react";
import { useRemixForm } from "remix-hook-form";
import { Form } from "~/components/Form";
import { FormTextarea } from "~/components/FormTextarea";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { cancelRFISchema } from "../../schemas/cancel-rfi-schema";

export function CancelRFIDialog({ routeName }: { routeName: string }): ReactNode {
  const navigate = usePreserveQueryNavigate();
  const fetcher = useFetcher();
  const navigation = useNavigation();
  const { id } = useParams();
  const isSubmitting = navigation.state === "submitting"
  const [isOpen, setIsOpen] = useState(false)
  const formMethods = useRemixForm<CancelRFISchema>({
    mode: "onSubmit",
    stringifyAllValues: false,
    defaultValues: {
      reason: "",
    },
    resolver: zodResolver(cancelRFISchema),
    submitHandlers: {
      onValid: (data) => {
        const { reason } = data
        fetcher.submit({ data: JSON.stringify({ reason }) }, { method: "post" })
        setIsOpen(false)
      },
    },
  });
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    formMethods.reset()
    if (!open) {
      navigate(`/${routeName}/${id}`)
    }
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-screen-md">
        <Form formMethods={formMethods} remixFormProps={{ method: "post" }}>
          <DialogHeader className="flex flex-row items-center gap-2 pb-5">
            <Info className="text-primary" />
            <DialogTitle>
              RFI Cancellation
            </DialogTitle>
          </DialogHeader>
          <p className="mb-4 text-sm text-gray-700">Are you sure you want to cancel this RFI?</p>
          <FormTextarea
            name="reason"
            label="Provide reason for action*"
            textareaProps={{ disabled: isSubmitting }}
          />
          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              Confirm
            </Button>
          </DialogFooter>

        </Form>
      </DialogContent>
    </Dialog>

  )
}
