import { Bad<PERSON>, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { ListAnnouncementDTO, ManagementListAnnouncementsData } from "~/services/api-generated";

type SortableColumns = NonNullable<NonNullable<ManagementListAnnouncementsData["query"]>["SortBy"]>;

export const sortableColumnNames = Object.keys({
  SendAt: null,
  Subject: null,
  MasterClientIds: null,
  JurisdictionIds: null,
  Status: null,
  MasterClientCodes: null,
  JurisdictionNames: null,
} satisfies Record<SortableColumns, null>)
const makeColumn = makeMakeColumn<SortableColumns, ListAnnouncementDTO>(sortableColumnNames)

export function useAnnouncementColumns() {
  const formatColDate = useFormatColDate();
  const columns = [
    makeColumn({ header: "Scheduled Date", id: "sendAt", accessorKey: "sendAt", cell: formatColDate("sendAt", { timezone: "UTC" }) }),
    makeColumn({ header: "Status", id: "status", accessorKey: "status" }),
    makeColumn({ header: "Subject", id: "subject", accessorKey: "subject" }),
    makeColumn({ header: "Master Clients", id: "masterClientCodes", accessorKey: "masterClientCodes", cell: (props) => {
      const allMasterClients = props.row.original.masterClientCodes || [];
      const displayedMasterClients = allMasterClients.slice(0, 3);
      const hasMore = allMasterClients.length > 3;

      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex flex-nowrap gap-1">
              {displayedMasterClients.map(masterClient => (
                <Badge key={masterClient} variant="info">
                  {masterClient}
                </Badge>
              ))}
              {hasMore && (
                <Badge variant="info">
                  +
                  {allMasterClients.length - 3}
                </Badge>
              )}
            </div>
          </TooltipTrigger>
          {hasMore && (
            <TooltipContent>
              <div className="flex flex-wrap gap-2 max-w-sm">
                {allMasterClients.map(masterClient => (
                  <Badge key={masterClient} variant="info">
                    {masterClient}
                  </Badge>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
      )
    } }),
    makeColumn({ header: "Jurisdiction", id: "jurisdictionNames", accessorKey: "jurisdictionNames", cell: (props) => {
      const jurisdictions = props.row.original.jurisdictionNames || [];
      const displayedJurisdictions = jurisdictions.slice(0, 3);
      const hasMore = jurisdictions.length > 3;

      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex flex-nowrap gap-1">
              {displayedJurisdictions.map(jurisdiction => (
                <Badge key={jurisdiction} variant="secondary">
                  {jurisdiction}
                </Badge>
              ))}
              {hasMore && (
                <Badge variant="secondary">
                  +
                  {jurisdictions.length - 3}
                </Badge>
              )}
            </div>
          </TooltipTrigger>
          {hasMore && (
            <TooltipContent>
              <div className="flex flex-wrap gap-2 max-w-sm">
                {jurisdictions.map(jurisdiction => (
                  <Badge key={jurisdiction} variant="secondary">
                    {jurisdiction}
                  </Badge>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
      )
    } }),
  ]

  return { columns }
}
