import type { ReactNode } from "react";
import { redirect } from "@remix-run/react";
import { CompleteRFIDialog } from "~/features/rfi/components/dialogs/CompleteRFIDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import type { CompleteRequestForInformationManagementDTO } from "~/services/api-generated";
import { completeRequestForInformation } from "~/services/api-generated";

const routeName = "request-for-information"
export const loader = makeEnhancedLoader(async ({ json }) => {
  return json(null);
}, { authorize: ["rfi.complete"] });

export const action = makeEnhancedAction(async ({ request, params, setNotification, json }) => {
  const { id } = params;
  if (!id) {
    setNotification({ title: "RFI ID is required", variant: "error" });

    return redirect("/request-for-information");
  }

  const formData = await request.formData()
  const data = formData.get("data") as string
  const body = JSON.parse(data) as CompleteRequestForInformationManagementDTO;
  const { error } = await completeRequestForInformation({ headers: await authHeaders(request), body, path: { requestForInformationId: id } });
  if (error) {
    setNotification({ title: "Failed completing RFI", variant: "error" })
  } else {
    setNotification({ title: "RFI completed", message: "RFI completed successfully", variant: "success" })
  }

  return json(null)
}, { authorize: ["rfi.complete"] })

export default function CompleteRFI(): ReactNode {
  return (<CompleteRFIDialog routeName={routeName} />)
}
