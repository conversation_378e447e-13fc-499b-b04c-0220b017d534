import type { ReactNode } from "react";
import { Button, SelectItem } from "@netpro/design-system";
import { Outlet, useLoaderData, useLocation, useNavigation, useSearchParams } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import queryString from "query-string";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormCombobox } from "~/components/FormCombobox";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useEsTbahColumns } from "~/features/economic-substance-tbah/hooks/use-es-tbah-columns";
import { relevantActivityOptions } from "~/features/economic-substance-tbah/types/relevant-activities";
import { submissionsSearchSchema } from "~/features/economic-substance-tbah/types/search-schema";
import { ReadableSubmissionStatusNames, SubmissionStatusNamesEnum } from "~/features/submissions/utilities/submission-status";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { ListSubmissionBahamasDTOPaginatedResponse, SubmissionStatus } from "~/services/api-generated";
import { managementBahamasListSubmissionsByModule } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Economic Substance",
    to: "/economic-substance/submissions",
  },
  title: "Submissions",
}

export const loader = makeEnhancedLoader(async ({ request, queryString, setNotification, json }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BAHAMAS });
  const schemaData = submissionsSearchSchema.safeParse(queryString).data || {};
  if (!schemaData?.hideDeleted) {
    schemaData.hideDeleted = "true"; // Default to hiding deleted submissions
  }

  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data: paginatedSubmissions, error } = await managementBahamasListSubmissionsByModule({ headers: await authHeaders(request), query: {
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfterDate,
    SubmittedBeforeDate: schemaData?.submittedBeforeDate,
    CompanyIncorporatedAfterDate: schemaData?.companyIncorporatedAfterDate,
    CompanyIncorporatedBeforeDate: schemaData?.companyIncorporatedBeforeDate,
    FinancialPeriodStartAt: schemaData?.financialPeriodStartAt,
    FinancialPeriodEndAt: schemaData?.financialPeriodEndAt,
    PaidAfterDate: schemaData?.paidAfterDate,
    PaidBeforeDate: schemaData?.paidBeforeDate,
    RelevantActivities: schemaData?.relevantActivities as string[] | undefined,
    Status: schemaData?.status as SubmissionStatus,
    GeneralSearchTerm: schemaData?.search,
    IsDeleted: schemaData?.hideDeleted === "true" ? false : schemaData?.hideDeleted === "false" ? true : undefined,
  } })

  if (error) {
    if (error.code === 8) {
      // Invalid sorting response
      setNotification({
        title: "Invalid data sorting",
        message: `Cannot sort the table by colum ${order}.`,
      })

      return json({
        paginatedSubmissions: { data: [], totalItemCount: 0 } as ListSubmissionBahamasDTOPaginatedResponse,
      })
    }

    // Unhandled API error
    console.error("Error fetching submissions", error);
    throw new Response("Currently unable to retrieve Economic Substance Bahamas submissions", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["es.bahamas.submissions.view"] })

export default function EconomicSubstanceBahamasSubmissionsLayout(): ReactNode {
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const location = useLocation();
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(submissionsSearchSchema);
  const { columns: submissionColumns } = useEsTbahColumns("submissions");
  const [searchParams] = useSearchParams()
  const {
    columns,
    submittedAfterDate,
    submittedBeforeDate,
    companyIncorporatedAfterDate,
    companyIncorporatedBeforeDate,
    financialPeriodStartAt,
    financialPeriodEndAt,
    paidAfterDate,
    paidBeforeDate,
    relevantActivities,
    status,
    search,
    hideDeleted,
  } = submissionsSearchSchema.parse(Object.fromEntries(searchParams))
  const queryParams = queryString.stringify({
    columns,
    submittedAfterDate,
    submittedBeforeDate,
    companyIncorporatedAfterDate,
    companyIncorporatedBeforeDate,
    financialPeriodStartAt,
    financialPeriodEndAt,
    paidAfterDate,
    paidBeforeDate,
    relevantActivities,
    status,
    search,
    location: location.pathname,
    hideDeleted,
  }, { arrayFormat: "bracket" })

  return (
    <>
      <CardContainer>
        <Authorized oneOf={["es.bahamas.submissions.search"]}>
          <Form formMethods={formMethods}>
            <FilterRow cols={5}>
              <FormColumnsFilter label="Visible Columns" columns={submissionColumns} />
              <FormDatePicker name="submittedAfterDate" label="Submitted After" />
              <FormDatePicker name="submittedBeforeDate" label="Submitted Before" />
              <FormDatePicker
                name="companyIncorporatedAfterDate"
                label="Company Incorporated After Date"
              />
              <FormDatePicker
                name="companyIncorporatedBeforeDate"
                label="Company Incorporated Before Date"
              />
              <FormDatePicker name="financialPeriodStartAt" label="Financial Period Starts Date" />
              <FormDatePicker name="financialPeriodEndAt" label="Financial Period End Date" />
              <FormDatePicker name="paidAfterDate" label="Date Paid After" />
              <FormDatePicker name="paidBeforeDate" label="Date Paid Before" />
              <FormSelect
                name="status"
                label="Status"
                selectValueProps={{ placeholder: "All" }}
                options={Object.values(SubmissionStatusNamesEnum).map((s) => {
                  if (s !== SubmissionStatusNamesEnum.SCHEDULED) {
                    return (
                      <SelectItem key={s} value={s}>{ReadableSubmissionStatusNames[s]}</SelectItem>
                    )
                  }

                  return null;
                })}
              />
              <FormSelect
                name="hideDeleted"
                label="Hide Deleted"
                selectValueProps={{ placeholder: "Yes", defaultValue: "true" }}
                options={["true", "false"].map((s: string) => (
                  <SelectItem key={s} value={s}>{s === "true" ? "Yes" : "No"}</SelectItem>
                ))}
              />
            </FilterRow>
            <FilterRow cols={5}>
              <div className="col-span-full">
                <FormCombobox
                  name="relevantActivities"
                  label="Relevant Activities"
                  options={relevantActivityOptions}
                  comboboxProps={{
                    placeholder: "Select an activity",
                    searchText: "Search...",
                    noResultsText: "No activities found.",
                    multiple: true,
                  }}
                />
              </div>
            </FilterRow>
            <FilterRow>
              <div className="col-span-full flex flex-row items-center gap-2">
                <FormSearch
                  name="search"
                  formItemProps={{ className: "w-full" }}
                  inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
                />
                <Button size="sm" className="gap-1.5" type="submit">
                  <Filter size={14} />
                  Apply Filter(s)
                </Button>
                <Authorized oneOf={["es.bahamas.submissions.export"]}>
                  <LinkButton
                    buttonProps={{
                      type: "button",
                      variant: "link",
                      size: "sm",
                      className: "gap-1.5",
                    }}
                    linkProps={{
                      to: {
                        pathname: "/economic-substance/submissions/export",
                        search: queryParams,
                      },
                      reloadDocument: true,
                    }}
                  >
                    Export as XLSX
                    <Download size={14} />
                  </LinkButton>
                </Authorized>
              </div>
            </FilterRow>
          </Form>
        </Authorized>
        <EnhancedTableContainer>
          <EnhancedTable
            sheetURL={row => `/economic-substance/submissions/${row.id}`}
            returnURL="/economic-substance/submissions"
            data={submissions}
            loading={<LoadingState isLoading={navigation.state === "loading"} />}
            rowId="id"
            columns={submissionColumns}
            totalItems={totalItemCount}
            defaultOpen={/^\/economic-substance\/submissions\/./.test(location.pathname)}
          />
        </EnhancedTableContainer>
      </CardContainer>
      <Outlet />
    </>
  );
}
