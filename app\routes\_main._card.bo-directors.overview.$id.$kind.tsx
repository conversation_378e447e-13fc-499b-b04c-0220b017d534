import type { ReactNode } from "react";
import { Outlet, useLoaderData } from "@remix-run/react";
import { useForm } from "react-hook-form";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { detailsSchema } from "~/features/bo-directors/schemas/detailsSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { BoDirEntity } from "~/lib/utilities/field-label-mapper";
import { mapFieldToLabel } from "~/lib/utilities/field-label-mapper";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";
import { managementGetBeneficialOwner, managementGetDirector } from "~/services/api-generated";

/**
 * TODO: When this gets more complicated, we might want to consider splitting this up into two views:
 *  /bo-directors/overview/$id/beneficial-owner and /bo-directors/overview/$id/director
 */
export const loader = makeEnhancedLoader(async ({ request, params, json }) => {
  await middleware(["auth"], request);
  const { data: routeParams, error } = detailsSchema.safeParse(params);

  if (error) {
    throw new Response("Incorrect Beneficial Owner / Director position defined.", { status: 412 });
  }

  if (routeParams.kind === "beneficial-owner") {
    const { data: beneficialOwner, error } = await managementGetBeneficialOwner({
      headers: await authHeaders(request),
      path: { beneficialownerId: params.id! },
    });

    if (error) {
      // Unhandled API error
      console.error("Error fetching Beneficial Owner", error);
      throw new Response("Could not retrieve the requested Beneficial Owner", { status: 412 });
    }

    return json({ beneficialOwner, director: null });
  } else if (routeParams.kind === "director") {
    const { data: director, error } = await managementGetDirector({
      headers: await authHeaders(request),
      path: { directorId: params.id! },
    });

    if (error) {
      // Unhandled API error
      console.error("Error fetching Director", error);
      throw new Response("Could not retrieve the requested Director", { status: 412 });
    }

    return json({ director, beneficialOwner: null });
  } else {
    throw new Response("Incorrect Beneficial Owner / Director position defined.", { status: 412 });
  }
}, { authorize: ["bo-dir.view"] });

export default function BoDirectorDetail(): ReactNode {
  const formMethods = useForm();
  const formatDate = useFormatDate();
  const { director, beneficialOwner } = useLoaderData<typeof loader>();
  const mapHeaderDate = (row: BoDirEntity, field: string): string => {
    // If is valid date, format it. Otherwise, return empty string
    const fieldValue = (row as any)[field];
    if (fieldValue && !Number.isNaN(Date.parse(fieldValue))) {
      return formatDate(fieldValue as string);
    }

    return "";
  };
  const getHeaders = () => {
    const boDir = (beneficialOwner || director) as BoDirEntity;

    // Check if metaData exists and has visibleFields
    if (boDir?.metaData?.visibleFields) {
      return boDir.metaData.visibleFields
        .filter((field: string): field is keyof BoDirEntity => {
          // Only include fields that actually exist on the entity
          return field in boDir;
        })
        .map((field: keyof BoDirEntity) => {
          const label = mapFieldToLabel(boDir, field);

          if (field.toLowerCase().includes("date")) {
            return [field, label, (row: BoDirEntity) => mapHeaderDate(row, field as string)];
          }

          return [field, label];
        });
    }

    return [];
  };

  return (
    <>
      <ActionSheetBody formMethods={formMethods}>
        <ActionSheetContent title={`${beneficialOwner ? "Beneficial Owner" : "Director"} details`}>
          <ActionSheetSection title="Details" collapsible>
            <ActionSheetDescriptionList
              data={beneficialOwner || director}
              headers={getHeaders()}
            />
            {/* {beneficialOwner && (
              <ActionSheetDescriptionList
                data={beneficialOwner}
                headers={beneficialOwner.isIndividual
                  ? [
                      // Individual BO
                      ["name", "Full Name"],
                      ["officerTypeName", "Type of Owner"],
                      ["dateOfBirth", "Date of Birth", row => row.dateOfBirth ? formatDate(row.dateOfBirth) : ""],
                      ["countryOfBirth", "Country of Birth", formatCountryOfBirth],
                      ["nationality", "Nationality"],
                      ["tin", "TIN"],
                      ["residentialAddress", "Residential Address"],
                    ]
                  : [
                      // Corporate BO
                      ["name", "Corporation Name"],
                      ["officerTypeName", "Type"],
                      ["incorporationNumber", "Incorporation Number"],
                      ["dateOfIncorporation", "Incorporation Date", row => row.dateOfIncorporation ? formatDate(row.dateOfIncorporation, { timezone: "UTC" }) : ""],
                      ["countryOfFormation", "Country of Formation"],
                      ["tin", "TIN"],
                      ["address", "Address"],
                      beneficialOwner.officerTypeCode === "KNTP05" ? ["stockExchange", "Stock Exchange Name"] : null,
                      beneficialOwner.officerTypeCode === "KNTP05" ? ["stockCode", "Stock Listing ID"] : null,
                    ]}
              />
            )}
            {director && (
              <ActionSheetDescriptionList
                data={director}
                headers={director.isIndividual
                  ? [
                      // Individual Director
                      ["name", "Full Name"],
                      ["officerTypeName", "Type of Officer"],
                      ["dateOfBirth", "Date of Birth", row => row.dateOfBirth ? formatDate(row.dateOfBirth) : ""],
                      ["countryOfBirth", "Place of Birth", formatCountryOfBirth],
                      ["nationality", "Nationality"],
                      ["appointmentDate", "Appointment Date", row => row.appointmentDate ? formatDate(row.appointmentDate) : ""],
                      ["residentialAddress", "Residential Address"],
                      ["serviceAddress", "Service Address"],
                    ]
                  : [
                      // Corporate Director
                      ["name", "Corporate Name"],
                      ["officerTypeName", "Type of Officer"],
                      ["incorporationNumber", "Incorporation Number"],
                      ["dateOfIncorporation", "Incorporation Date", row => row.dateOfIncorporation ? formatDate(row.dateOfIncorporation, { timezone: "UTC" }) : ""],
                      ["incorporationCountry", "Country of Formation"],
                      ["appointmentDate", "Appointment Date", row => row.appointmentDate ? formatDate(row.appointmentDate, { timezone: "UTC" }) : ""],
                      ["address", "Registered Office Address"],
                      ["serviceAddress", "Service Address"],
                    ]}
              />
            )} */}
          </ActionSheetSection>
        </ActionSheetContent>
        <ActionSheetFooter />
      </ActionSheetBody>
      <Outlet />
    </>
  )
}
