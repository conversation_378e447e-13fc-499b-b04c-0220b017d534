import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, DialogHeader, DialogTitle, Form as NetProForm, Spinner } from "@netpro/design-system";
import { Form as RemixForm, useLoaderData, useNavigation, useParams, useSubmit } from "@remix-run/react";
import { addYears, subDays } from "date-fns";
import { Repeat } from "lucide-react";
import { type ReactNode, useCallback } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { FormDatePicker } from "~/components/FormDatePicker";
import type { ChangeFinancialPeriodSchemaType } from "~/features/economic-substance-tbah/types/change-financial-period-schema";
import { changeFinancialPeriodSchema } from "~/features/economic-substance-tbah/types/change-financial-period-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { formatDateForAPI } from "~/lib/utilities/format";
import { managementGetSubmission, managementUpdateSubmissionInformation } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const submissionResponse = await managementGetSubmission({ headers: await authHeaders(request), path: { submissionId: params.id! }, query: { includeFormDocument: false } })
  if (!submissionResponse.data) {
    setNotification({ title: "The requested submission could not be found", variant: "error" })

    return redirect("/economic-substance/submissions")
  }

  return json({
    submission: {
      startDate: submissionResponse.data.startsAt,
      endDate: submissionResponse.data.endsAt,
    },
  })
}, {
  authorize: ["rfi.start"],
});

export const action = makeEnhancedAction(async ({ request, redirect, params, setNotification, json }) => {
  await middleware(["auth"], request);

  const { id } = params;

  if (!id) {
    throw new Response("Not Found", { status: 404 });
  }

  const formData = await request.formData()
  const dataBody = JSON.parse(formData.get("data") as string) as ChangeFinancialPeriodSchemaType
  const { endFinancialDate, startFinancialDate } = dataBody
  const { error } = await managementUpdateSubmissionInformation({
    headers: await authHeaders(request),
    path: { submissionId: id },
    body: { startAt: startFinancialDate, endAt: endFinancialDate },
  })

  if (error) {
    setNotification({ title: error.exceptionMessage as string, variant: "error" })

    return json({ success: false, error: error.exceptionMessage })
  }

  setNotification({ title: "Financial period changed successfully", variant: "success" })

  return redirect(`/economic-substance/submissions/${id}`)
}, {
  authorize: ["rfi.start"],
});

export default function EconomicSubstanceBahamasChangeFinancialPeriod(): ReactNode {
  const navigate = usePreserveQueryNavigate();
  const navigation = useNavigation()
  const submit = useSubmit()
  const { id } = useParams()
  const { submission } = useLoaderData<typeof loader>();
  const isSubmitting = navigation.state === "submitting"
  // form logic
  const form = useForm<ChangeFinancialPeriodSchemaType>({
    resolver: zodResolver(changeFinancialPeriodSchema),
    defaultValues: {
      startFinancialDate: submission.startDate || undefined,
      endFinancialDate: submission.endDate || undefined,
    },
  });

  function onSubmit(data: ChangeFinancialPeriodSchemaType): void {
    const { startFinancialDate, endFinancialDate } = data
    const startDate = formatDateForAPI(startFinancialDate)
    const endDate = formatDateForAPI(endFinancialDate)
    const body = JSON.stringify({ ...data, startFinancialDate: startDate, endFinancialDate: endDate })
    submit({ data: body }, { method: "post" })
  }

  const handleStartDateChange = useCallback((value: Date | undefined) => {
    const startDate = formatDateForAPI(value, { short: true })
    form.setValue("startFinancialDate", startDate);
    if (value) {
      const newDate = subDays(addYears(value, 1), 1);
      form.setValue("endFinancialDate", formatDateForAPI(newDate, { short: true }));
    }
  }, [form]);

  return (
    <Dialog open onOpenChange={() => navigate(`/economic-substance/submissions/${id}`)}>
      <DialogContent>
        <FormProvider {...form}>
          <NetProForm {...form}>
            <RemixForm
              onSubmit={form.handleSubmit(onSubmit)}
              method="post"
              noValidate
            >
              <DialogHeader className="flex flex-row items-center gap-2 pb-5">
                <Repeat className="text-primary" />
                <DialogTitle>
                  Change Financial Period
                </DialogTitle>
              </DialogHeader>
              <FormDatePicker
                name="startFinancialDate"
                label="Start Financial Date*"
                datePickerProps={{
                  disabled: isSubmitting,
                  disabledDates: { after: new Date() },
                  onChange: (value: Date) => handleStartDateChange(value as Date | undefined),
                } as any}
                formItemProps={{ className: "w-full" }}
              />
              <FormDatePicker
                name="endFinancialDate"
                label="End Financial Date*"
                datePickerProps={{
                  disabled: isSubmitting,
                  disabledDates: { after: new Date() },
                } as any}
                formItemProps={{ className: "w-full" }}
              />
              <DialogFooter className="pt-4">
                <Button
                  disabled={isSubmitting}
                  variant="outline"
                  type="button"
                  onClick={() => navigate(`/economic-substance/submissions/${id}`)}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSubmitting}
                  type="submit"
                >
                  {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Confirm"}
                </Button>
              </DialogFooter>
            </RemixForm>
          </NetProForm>
        </FormProvider>
      </DialogContent>
    </Dialog>

  )
}
