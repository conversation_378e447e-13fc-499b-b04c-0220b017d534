# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.18.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.29&targetVersion=GTv1.18.0&_a=files) (2025-09-19)

## [1.18.0-beta.29](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.28&targetVersion=GTv1.18.0-beta.29&_a=files) (2025-09-18)

## [1.18.0-beta.28](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.27&targetVersion=GTv1.18.0-beta.28&_a=files) (2025-09-18)

## [1.18.0-beta.27](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.26&targetVersion=GTv1.18.0-beta.27&_a=files) (2025-09-16)

## [1.18.0-beta.26](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.25&targetVersion=GTv1.18.0-beta.26&_a=files) (2025-09-16)

## [1.18.0-beta.25](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.24&targetVersion=GTv1.18.0-beta.25&_a=files) (2025-09-16)


### Features

* **Announcements:** add logic to display only 3 masterclients-jurisdictions, refers [#19247](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/19247) ([5cd1241](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5cd12414e32cf0e46cc560bc8cb0a39e46bee864))

## [1.18.0-beta.24](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.23&targetVersion=GTv1.18.0-beta.24&_a=files) (2025-09-15)

## [1.18.0-beta.23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.22&targetVersion=GTv1.18.0-beta.23&_a=files) (2025-09-10)

## [1.18.0-beta.22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.21&targetVersion=GTv1.18.0-beta.22&_a=files) (2025-09-10)

## [1.18.0-beta.21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.20&targetVersion=GTv1.18.0-beta.21&_a=files) (2025-09-10)

## [1.18.0-beta.20](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.19&targetVersion=GTv1.18.0-beta.20&_a=files) (2025-09-09)

## [1.18.0-beta.19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.18&targetVersion=GTv1.18.0-beta.19&_a=files) (2025-09-05)

## [1.18.0-beta.18](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.17&targetVersion=GTv1.18.0-beta.18&_a=files) (2025-09-05)


### Bug Fixes

* implement correct filters for ES submissions, refers [#18785](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18785), [#18786](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18786) ([e1da1c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e1da1c105138ff97b23babbf56b83dfddbc060b2))

## [1.18.0-beta.17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.16&targetVersion=GTv1.18.0-beta.17&_a=files) (2025-09-05)


### Bug Fixes

* **ES Bahamas:** fix resubmitted date for IRD export, refers [#19019](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/19019) ([d9d2449](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d9d24494640690c07f5f65c21cb40abb9356186b))

## [1.18.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.15&targetVersion=GTv1.18.0-beta.16&_a=files) (2025-09-03)


### Bug Fixes

* fix reset of datepickers, refers [#19021](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/19021) ([995c23e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/995c23e3a82cafdf5d0589278c8dbed70b1625bb))

## [1.18.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.14&targetVersion=GTv1.18.0-beta.15&_a=files) (2025-09-02)

## [1.18.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.13&targetVersion=GTv1.18.0-beta.14&_a=files) (2025-09-02)


### Features

* **RquestForInformation:** Integrated RFI screens with new API endpoints, refers [#18625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18625), [#18626](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18626) ([715657e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/715657eed97f0b7442c07b5f2245e32cd7f4e43c))

## [1.18.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.12&targetVersion=GTv1.18.0-beta.13&_a=files) (2025-09-02)


### Features

* **Submissions:** add Is deleted column and refactor BFR table, refers [#19046](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/19046) ([4cdfdda](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4cdfdda18c228ee2ddb5b7e87ad46b987254c710))

## [1.18.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.11&targetVersion=GTv1.18.0-beta.12&_a=files) (2025-09-02)


### Features

* **Menu:** change order and delete unused routes, refers [#19060](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/19060) ([eb90afd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/eb90afd02ee708e757e695f2a2b6f9b63dd745c0))

## [1.18.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.10&targetVersion=GTv1.18.0-beta.11&_a=files) (2025-08-29)


### Bug Fixes

* **Announcements:** new announcements should not be readonly, refers [#18818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18818) [#18752](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18752) ([5f77ec3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5f77ec3b260860d91b353283bc156ddf37626acf))

## [1.18.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.9&targetVersion=GTv1.18.0-beta.10&_a=files) (2025-08-29)

## [1.18.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.7&targetVersion=GTv1.18.0-beta.9&_a=files) (2025-08-27)


### Bug Fixes

* **ESSubmissions:** download doc button should be disabled when there arent docs, refers [#18679](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18679) ([056b328](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/056b328f0e1b779680ed565a0f0787afcb620bbe))
* **Migration:** renamed Nevis to Bahamas, refers [#18868](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18868) [#16028](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16028) ([88717df](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/88717df3621f887f61149967d57f9b7c243be1bc))
* **Submissions:** long lists should be scrollable, refers [#18741](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18741) ([2e2e293](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2e2e293874872564a50288bbd9c9528b55cab726))

## [1.18.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.7&targetVersion=GTv1.18.0-beta.8&_a=files) (2025-08-27)


### Bug Fixes

* **ESSubmissions:** download doc button should be disabled when there arent docs, refers [#18679](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18679) ([056b328](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/056b328f0e1b779680ed565a0f0787afcb620bbe))
* **Submissions:** long lists should be scrollable, refers [#18741](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18741) ([2e2e293](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2e2e293874872564a50288bbd9c9528b55cab726))

## [1.18.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.6&targetVersion=GTv1.18.0-beta.7&_a=files) (2025-08-22)


### Bug Fixes

* **ES:** show financial year in payment dialog, refers [#18733](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18733) ([cba3e12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/cba3e12e87d3996c5a4e582c5f7e1c69c2b3c359))

## [1.18.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.5&targetVersion=GTv1.18.0-beta.6&_a=files) (2025-08-22)

## [1.18.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.4&targetVersion=GTv1.18.0-beta.5&_a=files) (2025-08-21)


### Bug Fixes

* allow row selection by clicking row for payments, refers [#18799](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18799) ([f0ebdf8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f0ebdf894f6a772e7c1aaa32001a8ea3fbb5216c))

## [1.18.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.3&targetVersion=GTv1.18.0-beta.4&_a=files) (2025-08-21)


### Bug Fixes

* **Submissions:** added missing sorting, refers [#18361](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18361) ([4947341](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/494734132141ccdc42811f717c167adda064761b))

## [1.18.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.2&targetVersion=GTv1.18.0-beta.3&_a=files) (2025-08-21)


### Features

* **Historical filings:** add initial UI, refers [#18541](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18541) ([65e6868](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/65e6868ae9d027722f8b630af020ef81c38180cf))

## [1.18.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.1&targetVersion=GTv1.18.0-beta.2&_a=files) (2025-08-21)


### Bug Fixes

* **Announcements:** added sorting for 2 remaining  columns, refers [#18375](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18375) ([397e477](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/397e477ae5b10da4fe9404368d24a9800c447f40))

## [1.18.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.18.0-beta.0&targetVersion=GTv1.18.0-beta.1&_a=files) (2025-08-21)

## [1.18.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0&targetVersion=GTv1.18.0-beta.0&_a=files) (2025-08-20)


### Features

* **FeatureFlags:** Quickly enable and disable features using feature flags ([9c11888](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9c11888582b797b02bef5ada45a152b4fd62aa58))

## [1.17.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.51&targetVersion=GTv1.17.0&_a=files) (2025-08-18)

## [1.17.0-beta.51](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.50&targetVersion=GTv1.17.0-beta.51&_a=files) (2025-08-15)

## [1.17.0-beta.50](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.49&targetVersion=GTv1.17.0-beta.50&_a=files) (2025-08-15)

## [1.17.0-beta.49](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.48&targetVersion=GTv1.17.0-beta.49&_a=files) (2025-08-13)

## [1.17.0-beta.48](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.47&targetVersion=GTv1.17.0-beta.48&_a=files) (2025-08-13)


### Bug Fixes

* **Submissions:** Reorder and refactor of submission table columns, refers [#18668](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18668) ([2cfb531](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2cfb5313cb143b11494f4b42a1921b721b9d22cc))

## [1.17.0-beta.47](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.46&targetVersion=GTv1.17.0-beta.47&_a=files) (2025-08-11)


### Bug Fixes

* **Payments:** Reorder and refactor of payments table columns ([5d858fd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5d858fd2d4c4591d33c275e54d3419084376f871)), closes [#11765](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11765) [#18671](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18671)

## [1.17.0-beta.46](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.45&targetVersion=GTv1.17.0-beta.46&_a=files) (2025-08-11)


### Bug Fixes

* **Submissions:** Reorder and refactor of export submissions table columns ([80ff727](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/80ff727224b938314701b708df45173fc7db4ee4)), closes [#11765](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11765) [#18670](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18670)

## [1.17.0-beta.45](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.44&targetVersion=GTv1.17.0-beta.45&_a=files) (2025-08-11)


### Bug Fixes

* **Announcements:** update types and announcements logic, refers [#18529](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18529), [#18583](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18583) ([b8832a6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b8832a667d8e154bb4f8db327a5ffe813eb613d0))

## [1.17.0-beta.44](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.43&targetVersion=GTv1.17.0-beta.44&_a=files) (2025-08-06)

## [1.17.0-beta.43](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.42&targetVersion=GTv1.17.0-beta.43&_a=files) (2025-08-05)


### Features

* **RFI:** add RFI table and details UI with logic using mock data, refers [#18158](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18158), [#18159](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18159), [#18160](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18160), [#18579](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18579), [#18581](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18581), [#18616](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18616) ([51f4510](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/51f4510356e7a7900e39bfb77d731a9bbe177ecd))

## [1.17.0-beta.42](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.41&targetVersion=GTv1.17.0-beta.42&_a=files) (2025-08-05)


### Bug Fixes

* **Timezones:** fix edge cases for non considered formats, refers [#18623](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18623) ([17ac641](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/17ac64161727601d008326e83cd21c3c4bad169a))

## [1.17.0-beta.41](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.40&targetVersion=GTv1.17.0-beta.41&_a=files) (2025-07-30)


### Bug Fixes

* **ES:** refactor ES Submission columns and fix payment status, refers [#18582](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18582) ([c2fbe4c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c2fbe4cc00b06f188ea06354fb752b83adfb6386))

## [1.17.0-beta.40](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.39&targetVersion=GTv1.17.0-beta.40&_a=files) (2025-07-30)


### Features

* **RFI:** update texts for RFI sheet and modal, refers [#18560](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18560), [#18561](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18561) ([4238b51](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4238b51d55084a442063672c3d8d89471e3fe787))

## [1.17.0-beta.39](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.38&targetVersion=GTv1.17.0-beta.39&_a=files) (2025-07-25)


### Bug Fixes

* **Security:** Remove authentication info from session, refers [#18281](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18281) ([d28ea04](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d28ea0418b557a56815b1c832e41cd7f8eb29a83))

## [1.17.0-beta.38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.37&targetVersion=GTv1.17.0-beta.38&_a=files) (2025-07-24)

## [1.17.0-beta.37](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.36&targetVersion=GTv1.17.0-beta.37&_a=files) (2025-07-24)


### Features

* allow viewing deleted submissions for STR and BFR, refers [#18068](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18068), [#18069](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18069) ([edb7197](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/edb719700d834499781f33e16ae978af299b5839))

## [1.17.0-beta.36](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.35&targetVersion=GTv1.17.0-beta.36&_a=files) (2025-07-24)


### Features

* update dates logic to fix multiple errors, use consistent logic and jurisdiction timezones, refers [#14525](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14525) ([a36c0a6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a36c0a636ee6773f2940b9e2c933309b476f9f83))

## [1.17.0-beta.35](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.34&targetVersion=GTv1.17.0-beta.35&_a=files) (2025-07-24)


### Features

* **BO:** add new columns for KNTP05, refers [#15708](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15708) ([297face](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/297faceebf8fd4fcf5e1573eaf12596a0504fcaf))

## [1.17.0-beta.34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.33&targetVersion=GTv1.17.0-beta.34&_a=files) (2025-07-21)


### Features

* **Sorting:** added all the missing sorting functionality in the frontend ([e78913c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e78913c352be9bd43b601a8825f46f293962a48c))

## [1.17.0-beta.33](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.32&targetVersion=GTv1.17.0-beta.33&_a=files) (2025-07-16)


### Features

* added filter in ES to show/hide deleted submissions based on the isDeleted flag, refers [#17210](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17210) ([e9d05d3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e9d05d3c7ca3dfc598fa3d5af73b45433f6e6da5))

## [1.17.0-beta.32](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.31&targetVersion=GTv1.17.0-beta.32&_a=files) (2025-07-15)


### Features

* Build-ui-start-RFI-mngt, refers [#17934](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17934) [#18151](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18151) [#18355](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18355) ([1a62a57](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1a62a576497f293f9c504434ed36013b6a189f8a))

## [1.17.0-beta.31](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.30&targetVersion=GTv1.17.0-beta.31&_a=files) (2025-07-15)


### Features

* Add filter and ensure only active MCC's with active companies receive msgs, refers [#18149](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18149) ([430e053](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/430e053d5e42b73154d8a05ef1ca5f44d4b5befe))

## [1.17.0-beta.30](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.29&targetVersion=GTv1.17.0-beta.30&_a=files) (2025-07-10)

## [1.17.0-beta.29](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.28&targetVersion=GTv1.17.0-beta.29&_a=files) (2025-07-10)

## [1.17.0-beta.28](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.27&targetVersion=GTv1.17.0-beta.28&_a=files) (2025-07-10)


### Features

* **Users:** hide block/unblock button for management users, refers [#18188](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18188) ([585c4c3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/585c4c34c6fb7244a8c877b69de7302e8a980377))

## [1.17.0-beta.27](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.26&targetVersion=GTv1.17.0-beta.27&_a=files) (2025-07-09)


### Features

* hide initial results for ownership and officers table, refers [#17970](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17970) ([583f3b5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/583f3b55d545878485a6105229b300a78389a1bb))

## [1.17.0-beta.26](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.25&targetVersion=GTv1.17.0-beta.26&_a=files) (2025-07-09)


### Bug Fixes

* viewing a sent announcement opens in edit mode, incorrect route when canceling an announcement, refers [#18077](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18077) [#18076](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18076) [#17780](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17780) [#18004](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18004) ([caa9884](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/caa9884e31312744e2f02e15e66ea3ccca30ee3c))

## [1.17.0-beta.25](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.24&targetVersion=GTv1.17.0-beta.25&_a=files) (2025-07-08)


### Features

* update content of STR payments modal, refers [#17810](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17810) ([c175710](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c175710d48911f7a753df2d79a065a65ddd47a2a))

## [1.17.0-beta.24](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.23&targetVersion=GTv1.17.0-beta.24&_a=files) (2025-07-08)


### Features

* **BO:** update column mapping and order, refers [#17271](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17271), [#17457](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17457), [#17459](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17459) ([79915fa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/79915fa865b6b93d2bb36ed3f8f8c71d034e00b0))

## [1.17.0-beta.23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.22&targetVersion=GTv1.17.0-beta.23&_a=files) (2025-07-08)


### Features

* **Users:** add type to user details, refers [#18190](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18190) ([5a19bd0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5a19bd078cbbff0957e12a1710776526d276e02d))

## [1.17.0-beta.22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.21&targetVersion=GTv1.17.0-beta.22&_a=files) (2025-07-07)


### Bug Fixes

* update management text, refers [#18279](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18279) ([0f9d496](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0f9d49673277dfcf8f20954652912bd01027d9b8))

## [1.17.0-beta.21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.20&targetVersion=GTv1.17.0-beta.21&_a=files) (2025-07-07)

## [1.17.0-beta.20](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.19&targetVersion=GTv1.17.0-beta.20&_a=files) (2025-07-04)


### Bug Fixes

* userdialog-texupdate18189 ([de4f72f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/de4f72fce9cea0cd25f407fb9a345a15ca7a56ce))

## [1.17.0-beta.19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.18&targetVersion=GTv1.17.0-beta.19&_a=files) (2025-07-04)


### Bug Fixes

* mgmt-textupdate-18152 ([3e8576c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3e8576cb47ad601ffa7387b8785876dd32dd68fa))

## [1.17.0-beta.18](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.17&targetVersion=GTv1.17.0-beta.18&_a=files) (2025-07-04)

## [1.17.0-beta.17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.16&targetVersion=GTv1.17.0-beta.17&_a=files) (2025-06-27)


### Bug Fixes

* Remaining bugs for sprint 27 ([c52089c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c52089c0adacbde41e2ae48dbda74f951cdefeb2))

## [1.17.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.15&targetVersion=GTv1.17.0-beta.16&_a=files) (2025-06-24)

## [1.17.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.14&targetVersion=GTv1.17.0-beta.15&_a=files) (2025-06-19)

## [1.17.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.13&targetVersion=GTv1.17.0-beta.14&_a=files) (2025-06-19)


### Bug Fixes

* mgmt-text-update - labels, titles ([04389fa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/04389fa2c27709cc2c49b0bc202aaaf99eb70738))

## [1.17.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.12&targetVersion=GTv1.17.0-beta.13&_a=files) (2025-06-18)


### Bug Fixes

* cancel flow RFI and add loading ([7e43b5f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7e43b5f811d3eb3a1b8e148c7dee0faa685b0eac))

## [1.17.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.11&targetVersion=GTv1.17.0-beta.12&_a=files) (2025-06-18)


### Bug Fixes

* dates on the frontend should always be in UTC format to avoid timezone i... ([5655287](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5655287c2d894ba60aacb557a8abda633d6f5b4f))

## [1.17.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.10&targetVersion=GTv1.17.0-beta.11&_a=files) (2025-06-17)

## [1.17.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.9&targetVersion=GTv1.17.0-beta.10&_a=files) (2025-06-16)


### Bug Fixes

* Use the financial period from the submission instead of the form ([053c9aa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/053c9aace6dcfb17a2d781feaff0bfefcf26d0ee))

## [1.17.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.8&targetVersion=GTv1.17.0-beta.9&_a=files) (2025-06-12)

## [1.17.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv1.17.0-beta.7&targetVersion=GTv1.17.0-beta.8&_a=files) (2025-06-12)

## [1.17.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.6&targetVersion=GTv1.17.0-beta.7&_a=files) (2025-06-11)

## [1.17.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.5&targetVersion=GTv1.17.0-beta.6&_a=files) (2025-06-09)

## [1.17.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.4&targetVersion=GTv1.17.0-beta.5&_a=files) (2025-06-06)

## [1.17.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.3&targetVersion=GTv1.17.0-beta.4&_a=files) (2025-06-06)


### Bug Fixes

* Changed GET to POST to fix too long url in IRD export, fixes [#17568](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17568) ([329db2b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/329db2b949f56e4bbf40d032ec59f770339e4092))

## [1.17.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.2&targetVersion=GTv1.17.0-beta.3&_a=files) (2025-06-05)


### Bug Fixes

* management update 17481 ([97aae5b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/97aae5b93ac9bb0d5db2f6cb03ff3e9d2667e001))

## [1.17.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.1&targetVersion=GTv1.17.0-beta.2&_a=files) (2025-06-05)


### Bug Fixes

* Management bugs: Extra logging and sort issue ([4cf0b37](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4cf0b37391d1dc271021b155de262503fbe0e291))

## [1.17.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.0&targetVersion=GTv1.17.0-beta.1&_a=files) (2025-06-04)

## [1.17.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-alpha.2&targetVersion=GTv1.17.0-beta.0&_a=files) (2025-05-26)

## [1.17.0-alpha.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-alpha.1&targetVersion=GTv1.17.0-alpha.2&_a=files) (2025-05-26)


### Bug Fixes

* Fixed typo in production office: TYCP->TCYP, fixes [#17463](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17463) ([3e93e32](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3e93e32cb52fd16928145d0fd296ac7cc89d7435))

## [1.17.0-alpha.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-alpha.0&targetVersion=GTv1.17.0-alpha.1&_a=files) (2025-05-23)


### Bug Fixes

* update labels, refers [#17409](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17409) ([6936940](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/693694047235ed22fe09b688e19e6f158cf4c38a))

## [1.17.0-alpha.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.4&targetVersion=GTv1.17.0-alpha.0&_a=files) (2025-05-23)


### Features

* Show app version in UI, refers [#17393](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17393) ([94f3b97](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/94f3b97893ca4312045b056a6cf783f9a2b3a984))

## [1.17.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.3&targetVersion=GTv1.17.0-beta.4&_a=files) (2025-05-20)

## [1.17.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.2&targetVersion=GTv1.17.0-beta.3&_a=files) (2025-05-19)

## [1.17.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.1&targetVersion=GTv1.17.0-beta.2&_a=files) (2025-05-13)


### Bug Fixes

* Don't disable submissions log based on status, refers  [#16184](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16184) ([54e9dac](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/54e9dac128ddaad47315cd66758a121469deb7e2))

## [1.17.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.17.0-beta.0&targetVersion=GTv1.17.0-beta.1&_a=files) (2025-05-09)


### Bug Fixes

* Creation of RFI was failing due to missing header ([871a8ac](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/871a8acf31309dbf228b17525d910c2cd7347e20))

## [1.17.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.16.0&targetVersion=GTv1.17.0-beta.0&_a=files) (2025-05-07)


### Features

* add ES Bah module to company overview and annual fee, refers [#12387](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12387), [#12430](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12430) ([3a0de77](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3a0de774800ae5625cea3ba8e6fe7a464653cbce))
* add logic to approve ES Bahamas, refers [#12387](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12387) ([ef3525d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ef3525dd0b61f1aaea0f862bc71b4e8ee1d6b089))
* added financial returns ui refers [#15125](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15125) [#15126](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15126) [#15127](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15127) [#15128](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15128) [#15130](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15130) [#15131](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15131) ([1c90d05](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1c90d05f19365c6e992d158e77d576cab77738e6))
* **Announcements:** created form, added create, update, delete functionality, closes [#14975](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14975), [#14974](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14974), [#14976](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14976), [#14956](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14956) ([ff0f54c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ff0f54c05309763d9858cf88d4cd2b4a175feac3))
* **CI:** Enable deploy of dev-tbah to dev environment ([b86735a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b86735aff637c5f4f3380c78dc057e235ba56f06))
* **TBAH Submissions:** added submissions page, payments page, ita export page, rfi dialog, supported documents flow, submission pdf, mark as paid, bulk import, ita export pdf, export evidence files ([b8cddc6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b8cddc692a7c591d407d757b4417acb798656694))


### Bug Fixes

* bug fixes for ES Bahamas, refers [#15864](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15864), [#16055](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16055), [#16056](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16056), [#16065](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16065), [#16048](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16048) ([055e762](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/055e762cfafc737caaf3fedb24e9964599ac71b3))
* fix breadcrumb, email in PDF and filters, refers [#16213](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16213), [#16297](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16297), [#16301](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16301), [#16302](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16302) ([95a33b4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/95a33b461b38a688f0ac41522483465923a6c6e3))
* fix lint ([9f0719b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9f0719ba7ef9bc6298061d16bab10cf7f18151e5))
* improve error management and consider empty objects, refers [#15794](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15794) ([5cad1fb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5cad1fb1954f36426486e8e7da5a05099d755047))
* multiple bug fixes, refers [#15794](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15794), [#15852](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15852), [#15853](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15853), [#15862](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15862), [#15863](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15863), [#15878](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15878), [#15920](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15920), [#15937](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15937), [#15938](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15938) ([c4159f1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c4159f1f058d4c0b51d3dee0a98f6ee66de9bc38))
* multiple ES Bah fixes for submissions, refers [#16062](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16062), [#16064](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16064), [#16183](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16183) ([48e5ce5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/48e5ce51e6577f51b853445fb1ce431365b0c54b))

## [1.16.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.16.0-beta.0&targetVersion=GTv1.16.0&_a=files) (2025-05-06)

## [1.16.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.2&targetVersion=GTv1.16.0-beta.0&_a=files) (2025-05-02)


### Features

* add support for "no-country" submission filter, refers [#16907](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16907) ([6f181a7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6f181a7f960720329f1d773bac1003db35d05b51))

## [1.15.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.2-beta.0&targetVersion=GTv1.15.2&_a=files) (2025-04-10)

## [1.15.2-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.1&targetVersion=GTv1.15.2-beta.0&_a=files) (2025-04-10)


### Bug Fixes

* add no country logic, refers [#16464](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16464) ([2215967](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/22159676ac1b57686ac96ada7d2eefb2e4ac8e5e))

## [1.15.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.1-beta.1&targetVersion=GTv1.15.1&_a=files) (2025-04-07)

## [1.15.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.1-beta.0&targetVersion=GTv1.15.1-beta.1&_a=files) (2025-04-07)


### Bug Fixes

* fix saving logic for company settings, refers [#16353](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16353) ([722d166](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/722d166d3c9cf406dfe1ada4b4032ab0d8ac8d77))

## [1.15.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.0&targetVersion=GTv1.15.1-beta.0&_a=files) (2025-04-07)

## [1.15.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.0-beta.1&targetVersion=GTv1.15.0&_a=files) (2025-04-07)

## [1.15.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.15.0-beta.0&targetVersion=GTv1.15.0-beta.1&_a=files) (2025-04-07)


### Bug Fixes

* fix condition for annual fee, refers [#16346](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16346) ([5c84dd8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5c84dd891e0a9ac4aaddc00965f2a4ebd67806da))

## [1.15.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.14.0&targetVersion=GTv1.15.0-beta.0&_a=files) (2025-04-04)


### Features

* add annual fee logic, refers [#16307](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16307) ([ad4d186](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ad4d1867a2e42205464d5229bea7d1eac20cf457))
* add Netherland Antilles to country list, refers [#16272](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16272) ([9b133ff](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9b133ff8523ff679d340737d74d19cc8512e534d))

## [1.14.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.14.0-beta.0&targetVersion=GTv1.14.0&_a=files) (2025-03-28)

## [1.14.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.13.0&targetVersion=GTv1.14.0-beta.0&_a=files) (2025-03-28)


### Features

* add sync details screen, refers [#16004](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16004) ([7c0e165](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7c0e165058021e6843305b75784ddeaf6a4794e5))


### Bug Fixes

* fix HTTP interceptor issue, add table header and add download template, refers [#15470](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15470), [#16217](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16217), [#16237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16237) ([2bfa339](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2bfa3394e91249d6ec0a54a1e4c5d9227a0688fe))
* remove console errors no longer needed, refers [#13983](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13983) ([459f2e7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/459f2e7431bcdd4fdbb8ac8ea401bc35c9ab9cd2))

## [1.13.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.13.0-beta.0&targetVersion=GTv1.13.0&_a=files) (2025-03-12)

## [1.13.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.12.1&targetVersion=GTv1.13.0-beta.0&_a=files) (2025-03-11)


### Features

* add STR form for 2024, refers [#15800](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15800) ([63ae2b2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/63ae2b29b112bb891a9700fc26e0e7c8b12f828a))


### Bug Fixes

* label changes and officer type change, refers [#15805](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15805), [#15276](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15276) ([cb3d43c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/cb3d43c555c7d6321e0ea6971a4dd8c6db7fc8e3))

## [1.12.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.12.1-beta.0&targetVersion=GTv1.12.1&_a=files) (2025-02-26)

## [1.12.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.12.0&targetVersion=GTv1.12.1-beta.0&_a=files) (2025-02-26)


### Bug Fixes

* fix columns not populated for companies and filters not working, refers [#15709](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15709) ([c7e00e6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c7e00e66d7fdb1b4aedbe0c97d82e1aa0a423b15))

## [1.12.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.12.0-beta.0&targetVersion=GTv1.12.0&_a=files) (2025-02-20)

## [1.12.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.11.0&targetVersion=GTv1.12.0-beta.0&_a=files) (2025-02-19)


### Features

* update columns for BO/Dir, refers [#15519](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15519) ([4c9464b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4c9464b9f802c69acffd116fef356bf0ed0d91c9))


### Bug Fixes

* fix logic for custom fee and display default value, refers [#15521](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15521) ([4f419fe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4f419fe90471c7599c493e2d9d6769d546956a12))

## [1.11.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.10.1-beta.0&targetVersion=GTv1.10.1&_a=files) (2025-02-17)

## [1.10.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.10.0&targetVersion=GTv1.10.1-beta.0&_a=files) (2025-02-17)


### Bug Fixes

* update BO/Dirs to Ownership & Officers and remove legal entity name, refers [#15477](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15477), [#15482](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15482) ([b2e6d73](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b2e6d73d5ba303643c9f54df775bae99acfab2ba))

## [1.10.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.10.0-beta.2&targetVersion=GTv1.10.0&_a=files) (2025-02-13)

## [1.10.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.10.0-beta.1&targetVersion=GTv1.10.0-beta.2&_a=files) (2025-02-13)


### Features

* **CICD:** Use self-hosted agent pools ([d9f7710](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d9f7710074a9406aeb47e019bfc781f516cdbf70))

## [1.10.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.10.0-beta.0&targetVersion=GTv1.10.0-beta.1&_a=files) (2025-02-13)


### Bug Fixes

* add sorting to entityType, refers [#15277](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15277) ([1955425](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1955425f3a3c683bc5211a62f0cd030b888eaa49))
* add vpEntityStatus sort for companies, refers [#15259](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15259) ([1a641ae](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1a641ae53a7f10d4e0f811ec1e1e11e6fb805ca3))
* fix first submission year configuration and improve fee validations, refers [#15453](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15453) ([17e43aa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/17e43aad20700d15098d7ca7ecd075c846354e48))
* fix STR PDF conditions to display sections, refers [#15414](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15414), [#15412](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15412) ([2d34e54](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2d34e54385c55a2431cca09e10d560cb677fd453))

## [1.10.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.9.0&targetVersion=GTv1.10.0-beta.0&_a=files) (2025-02-10)


### Features

* add new columns and update PDF label, refers [#15262](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15262), [#15235](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15235), [#15255](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15255) ([8ea75da](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8ea75da7750d0834d312fd2e326fcac3ba1d97b2))


### Bug Fixes

* add missing columns to bo-dirs overview refers [#13191](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13191) ([7827804](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7827804f6468bc3ebd8cc0a2a9fe68b66ff24a93))
* company overview properly define sortable columns refers [#15259](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15259) ([32c5fe8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/32c5fe879619b95e0be48ead3a98fb4154c50663))
* make date formatting consistent refers part 2 [#15253](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15253) ([b5c1cee](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b5c1cee97b4c7d8b1e610c3c0de2f61e92c6733b))

## [1.9.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.9.0-beta.1&targetVersion=GTv1.9.0&_a=files) (2025-02-07)

## [1.9.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.9.0-beta.0&targetVersion=GTv1.9.0-beta.1&_a=files) (2025-02-07)

## [1.9.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.8.0&targetVersion=GTv1.9.0-beta.0&_a=files) (2025-02-07)


### Features

* add pagination hook for PDF generation, refers [#15025](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15025) ([f6f7aa1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f6f7aa19fd0ed88c14845cf74bf223726dce68f9))
* added download migration refers [#14652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14652) ([3a2cde3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3a2cde3bcecdd30737912848989b760c1e7815fd))
* update bo dir overview columns refers [#14971](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14971) [#15271](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15271) ([1fb0f46](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1fb0f46af271549bc84bdfc577e5576ef87109af))


### Bug Fixes

* make date formatting consistent refers [#15253](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15253) ([9e8a7a8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9e8a7a849c750f9836c24de91516dd8f633ade04))
* page title casing [#13437](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13437) ([d948190](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d9481901598b3c54ebaf5980f282e35f9ff8d709))
* pending onboarding detail permissions refers [#15133](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15133) ([c24b092](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c24b09259b466c1c0f587f9876bc592951d5a3b9))
* replace use correct enum in bo-dirs Production Office filter refers [#15062](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15062) ([6883e2d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6883e2dd9c2995218299ec1090705895690cefae))
* update column for STR, refers [#14994](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14994) ([954f776](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/954f776a80c049ea51b94077651d9b57d0a68ffa))

## [1.8.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.8.0-beta.2&targetVersion=GTv1.8.0&_a=files) (2025-02-03)

## [1.8.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.8.0-beta.1&targetVersion=GTv1.8.0-beta.2&_a=files) (2025-02-03)

## [1.8.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.8.0-beta.0&targetVersion=GTv1.8.0-beta.1&_a=files) (2025-02-03)

## [1.8.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.7.0&targetVersion=GTv1.8.0-beta.0&_a=files) (2025-02-03)


### Features

* add approval tooltip, refers [#15044](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15044) ([f8c404b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f8c404b50e726ca296c435225209ccf751e5197d))
* add entity type column, refers [#14903](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14903), [#14902](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14902) ([8f0aca6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8f0aca66973a795544850f66d857066cd0bc9869))
* add new fields and unify company details sections, refers [#14584](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14584) ([9464ab3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9464ab3361ee04c73c847593f85f0894720b8091))
* add search field for reports, refers [#15021](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15021) ([4a6d338](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4a6d338e3f87554fa5deca9ec23a340f26ba2033))
* company over make onboardingStatus optional and fix filter label [#14986](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14986) [#15079](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15079) ([caf8bdb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/caf8bdb48684fecb53bf3acbccd7fe9d7ebd5a73))


### Bug Fixes

* add column mapping for BO/Dir position, refers [#15005](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15005) ([1f2a176](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1f2a176e815833200c726e54431f3b97ce737911))
* add entity status filter to pending onboarding refers [#15080](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15080) ([b6bbbca](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b6bbbca8672d21f30ed8e97455bdc774bc6b6b48))
* hide ES substance for current TNEV deployment, refers [#15132](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15132) ([6360389](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/63603890426f975e507ecfa8d8b810902d6529b4))
* hide str except for IBC and LLC companies refers [#14901](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14901) ([515f20b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/515f20bc3cd63f1f6a3ac8b5fb40e85e3bdef43b))
* update company update so objects don't rewrite, refers [#15047](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15047), [#15051](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15051) ([d99e746](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d99e7463d7449c0a75fe7bc0e062db573187f8cb))
* update date format to dd-MM-yyyy, refers [#15006](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15006) ([f09a627](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f09a6278a06ca86266088dba3967ac0792a298a9))
* update VGPT for VGTP, refers [#15059](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15059) ([8d424c5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8d424c5954ed0c1e8ba46db895f0d29866950ebd))

## [1.7.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.7.0-beta.1&targetVersion=GTv1.7.0&_a=files) (2025-01-30)

## [1.7.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.7.0-beta.0&targetVersion=GTv1.7.0-beta.1&_a=files) (2025-01-29)

## [1.7.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0&targetVersion=GTv1.7.0-beta.0&_a=files) (2025-01-29)


### Features

* show all active/inactive companies in onboarding, refers [#14985](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14985) ([1a5a01e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1a5a01ef22747bc225ba7c6fe78d26ce9718ef80))


### Bug Fixes

* format date columns on bo-dirs overview refers [#15024](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15024) ([0119e48](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0119e483a402ca003122ad055697e04414bc4886))

## [1.6.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.8&targetVersion=GTv1.6.0&_a=files) (2025-01-28)

## [1.6.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.7&targetVersion=GTv1.6.0-beta.8&_a=files) (2025-01-28)

## [1.6.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.6&targetVersion=GTv1.6.0-beta.7&_a=files) (2025-01-28)


### Features

* add confirmation dialog to cleanup button, refers [#14991](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14991) ([6a9dddf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6a9dddf0e32a7b1d9f73287bf38283d4ce9d9289))
* text changes, minor updates and fixes, refers [#15027](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15027) ([5f34239](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5f342399503fb75d8a274008a23d8e55dc31abf9))

## [1.6.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.5&targetVersion=GTv1.6.0-beta.6&_a=files) (2025-01-27)

## [1.6.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.4&targetVersion=GTv1.6.0-beta.5&_a=files) (2025-01-27)


### Features

* implement clean-up migration endpoint refers [#14654](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14654) ([164470a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/164470a1a354f4a91de39a32c3b2acbd907919b2))

## [1.6.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.3&targetVersion=GTv1.6.0-beta.4&_a=files) (2025-01-24)

## [1.6.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.2&targetVersion=GTv1.6.0-beta.3&_a=files) (2025-01-24)


### Features

* add alert when not all modules are enabled and disabled changes when company is declined, refers [#14580](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14580), [#14583](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14583) ([f3975bf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f3975bfef5208ded7bbb6d83f5311d13641979ec))
* add VP Entity Status to companies overview table refers [#14579](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14579) ([6a40810](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6a408103112db564d11948c8aceee639fa059692))
* added payment status to str summary refers [#14702](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14702) ([6617c79](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6617c7974bed8071a458ad6543056d50301b5bbf))
* change table default row size to 10 refers [#14582](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14582) ([d6b3456](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d6b3456bf9efae2e3616868a46bcda074f8dacf4))

## [1.6.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.1&targetVersion=GTv1.6.0-beta.2&_a=files) (2025-01-22)


### Features

* add company address to invoices refers [#14625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14625) ([61c1254](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/61c125495839a22eac692e471969dc7eb96cdcd7))
* add log details, refers [#14576](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14576) ([ada196c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ada196c8d78ca315adcd1510f72a723ec8ddcf09))


### Bug Fixes

* add proper pagination for reports, refers [#14867](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14867) ([4a10983](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4a10983bb6f03cbcab02c990bb9124933b899c2c))
* remove duplicate breadcrumb segment refers [#14866](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14866) ([59f66d1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/59f66d105a7a0a28c8efcff4d1f89824a4a61efe))
* update onboarding filters, refers [#14007](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14007), [#14578](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14578) ([10bcb6c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/10bcb6c83d82af23ebb96a71db579a0c37362111))

## [1.6.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.6.0-beta.0&targetVersion=GTv1.6.0-beta.1&_a=files) (2025-01-21)

## [1.6.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.5.0&targetVersion=GTv1.6.0-beta.0&_a=files) (2025-01-21)


### Features

* add company log for onboarding companies, refers [#14575](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14575) ([38e3198](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/38e3198917d93fe45edcef8ce0a192fa53879001))


### Bug Fixes

* always show CorporateAccountingRecordsSummary and CorporateMultinationalEnterpriseSummary refers [#14635](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14635) ([ac16db7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ac16db77b8925e2a249642a7d78f1cf49eb23f55))
* auth issues refers [#14642](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14642) [#14360](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14360) ([cfec28c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/cfec28cc8889adb43689e8fb7b2b9171c9950713))
* hide declined companies from company overview refers [#14577](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14577) ([bd28af9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/bd28af904122c2a840a5f6fa219503d978d774a8))
* hide download button if STR submission is a draft refers [#14687](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14687) ([daf913c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/daf913c5132811ba62dc66416c9a399a540deff7))
* set columns that arent sortable on user overview refers [#14611](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14611) ([6f53b49](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6f53b499de054ca43dafe6b9baac352e746846b0))
* str late fee modals resetting [#14676](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14676) ([c9ae8d9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c9ae8d9b567b4721a42699735879bb372525cad8))
* update company overviews column headers and labels refers [#14563](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14563) ([ae04a2f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ae04a2fca4c037c82d45191e4f107a8bcc48fdfc))
* use legal entity data in STR pdf refers [#14756](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14756) ([b5ddbef](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b5ddbefa8e93e30e1f7bfa099ee3c5b795cdef79))
* weird behaviour on upload payments modal final step [#14677](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14677) ([1ecdcd1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1ecdcd1fe0a69f3ad86c39891bfd17f9febb42fd))

## [1.5.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.5.0-beta.0&targetVersion=GTv1.5.0&_a=files) (2025-01-10)

## [1.5.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0&targetVersion=GTv1.5.0-beta.0&_a=files) (2025-01-10)


### Features

* **Account Management:** added flow to edit companies account management for panama, refers [#14118](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14118) ([04e8ba0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/04e8ba007a84968f853e472021e778964c8fe63f))
* added economic substance bulk payment import ui refers [#14516](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14516) [#14515](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14515) ([6c63b13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6c63b134988b8247207328776b3c0af060ce4245))
* added economic substance ITA export UI refers [#14546](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14546) [#14547](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14547) [#14548](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14548) [#14549](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14549) [#14550](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14550) ([626c4f0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/626c4f025f379334c6b6382bd39a6c963233425e))
* economic substance payments ui refers [#14513](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14513) [#14514](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14514) ([546b74f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/546b74f921c01105823fdfe61af80a43685421c8))
* **Submissions:** process API change from SubmittedByEmail to CreatedByEmail, [#14527](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14527) ([9789c27](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9789c277f1c97a7ea88744c88417429617657bee))


### Bug Fixes

* add proper error display for submission error messages, refers [#14552](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14552) ([24b7086](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/24b7086007b60d6649726836ea48798b38dbad85))
* add TIN to bo-directors detail modal for Individual BO [#14512](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14512) ([2a98a3d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2a98a3d3e9899fd485bb0e5decd74b26f12ab526))
* allow MFA reset only for client users, refers [#14422](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14422) ([6a604a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6a604a248efff0aeb8af890505dd3df0ce40f351))
* refetch permissions when empty, refers [#14360](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14360) ([2ac0146](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2ac01463c3b6c0a509ec46123fc9996e0c2768d6))
* set correct column labels on companies overview and account management refers [#14539](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14539) ([58fc380](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/58fc380fad6ae7dcab748c67e7f80756258ba67d))
* update client secret, refers [#14541](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14541) ([ff08057](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ff0805739b2ab6e5f8688c4a915687ca1bcfb556))
* update token set logic to ensure cookie update, refers [#14360](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14360) ([46325fa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/46325faa85707d91eb5c33f2db931b89b441a995))

## [1.4.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.8&targetVersion=GTv1.4.0&_a=files) (2025-01-06)

## [1.4.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.7&targetVersion=GTv1.4.0-beta.8&_a=files) (2025-01-06)


### Bug Fixes

* **STR Fees:** Fix delete late payment fee not working,  [#14510](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14510) ([05425d0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/05425d0088e927fc8952d0e28ad3e73f6fe7c737))

## [1.4.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.6&targetVersion=GTv1.4.0-beta.7&_a=files) (2025-01-06)

## [1.4.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.5&targetVersion=GTv1.4.0-beta.6&_a=files) (2025-01-06)

## [1.4.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.4&targetVersion=GTv1.4.0-beta.5&_a=files) (2025-01-06)

## [1.4.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.3&targetVersion=GTv1.4.0-beta.4&_a=files) (2025-01-06)

## [1.4.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.2&targetVersion=GTv1.4.0-beta.3&_a=files) (2025-01-06)


### Features

* add user type to the user overview table refers [#14021](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14021) ([0b0cf4e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0b0cf4ee5c0ab867db7758532e5bb833697a14fa))
* economic substance submission ui refers [#14394](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14394) [#14392](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14392) [#14390](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14390) [#14389](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14389) [#14325](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14325) ([dc64991](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/dc6499140d2b203cd24a85344d7a5602a122f0b6))


### Bug Fixes

* add moduleId to payment import request, refers [#14147](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14147) ([a1d35af](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a1d35af0fbdedc542db399038786f2c51bf3b1fb))
* add sortable columns for BO/Dir and fix wrong name, refers [#14005](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14005) ([0e7b815](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0e7b815ab8072e3b00d1b58dad34b10274e8924d))
* allow only years after incorporation, refers [#13814](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13814) ([5e18ba8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5e18ba82469261641e603606978b425359388ae3))
* bo-dirs sidesheet not opening refers [#14344](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14344) ([cc2a913](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/cc2a913567bb89250ba09b0aed371745ce5e198b))
* enable pagination for STR payments, refers [#14403](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14403) ([b0c5925](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b0c5925026ebde90b55718d2be6cdae3537434ec))
* fix masterclient redirect on close, refers [#14343](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14343) ([02d614d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/02d614d6361c1310e08f3b743b1798d72e6fedae))
* set late filing fee description, refers [#14333](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14333) ([9d78ac8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9d78ac80ebbfa44107a9e8cf0ee6e50045dd4dae))
* **Simplified Tax Return:** update validation for late fee, refers [#14004](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14004) ([ce5cd3f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ce5cd3fd81700629a81c0ada23b83a81084a41ea))

## [1.4.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.1&targetVersion=GTv1.4.0-beta.2&_a=files) (2024-12-30)

## [1.3.1-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.1-beta.4&targetVersion=GTv1.3.1-beta.5&_a=files) (2024-12-30)

## [1.3.1-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.0&targetVersion=GTv1.3.1-beta.4&_a=files) (2024-12-30)

## [1.4.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.4.0-beta.0&targetVersion=GTv1.4.0-beta.1&_a=files) (2024-12-30)


### Features

* **Data Migration:** add data migration errors, refers [#14296](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14296) ([44b5e53](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/44b5e53acff739e17448ebd0109bdcc416e17c1a))

## [1.4.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.1-beta.3&targetVersion=GTv1.4.0-beta.0&_a=files) (2024-12-30)


### Features

* add managers to master clients overview refers [#13674](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13674) ([9dfa8ab](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9dfa8ab4da0a3c14a0f1c595a3f1d7199316d9c4))


### Bug Fixes

* allow loading state to be displayed when no results, refers [#14138](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14138) ([af86186](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/af86186b17af5d42909b5030d90425d41f4bbe34))
* display proper status, refers [#14250](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14250) ([aac3386](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/aac33868cd5909608a1ed64fefa3358880036788))
* negative number values should not be not be allowed str fees refers [#14263](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14263) [#14282](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14282) ([3406b6c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3406b6c68b3133d1d6211c03bbe51e170ec09487))
* preserve query params after opening sheet and doing action, refers [#13805](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13805), [#14129](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14129) ([aa50641](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/aa506411a1857d002ba9c9e0a174193bf4cc57f3))
* str payments import not working refers [#14147](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14147) ([3f204be](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3f204be7703b81aa4e1559ac8b8f7e33c478c641))
* update STR export layout to match other views, refers [#14283](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14283) ([96916c4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/96916c4d2bc606c47ee577e6223152a4c48f801c))

## [1.3.1-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.1-beta.2&targetVersion=GTv1.3.1-beta.3&_a=files) (2024-12-20)


### Bug Fixes

* add invoice date to PDF, refers [#14262](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14262) ([0e38967](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0e389678f4819df284f6f2ca0c4c8ab49cbbac56))
* enhance security by adding CSP and headers, refers [#14278](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14278) ([ea4b39f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ea4b39f061d50e97db7429835c9cb9dfb58b1abf))

## [1.3.1-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.1-beta.1&targetVersion=GTv1.3.1-beta.2&_a=files) (2024-12-19)

## [1.3.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.1-beta.0&targetVersion=GTv1.3.1-beta.1&_a=files) (2024-12-19)


### Bug Fixes

* Make error message when creating a a str late fee more human friendly, refers [#14251](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14251) ([0d1016a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0d1016a99e086e297b3475c0126003982103c478))

## [1.3.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0&targetVersion=GTv1.3.1-beta.0&_a=files) (2024-12-19)

## [1.3.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.10&targetVersion=GTv1.3.0&_a=files) (2024-12-16)

## [1.3.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.9&targetVersion=GTv1.3.0-beta.10&_a=files) (2024-12-16)

## [1.3.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.8&targetVersion=GTv1.3.0-beta.9&_a=files) (2024-12-16)


### Bug Fixes

* **Basic Financial Report:** implement correct API endpoint, refers [#14124](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14124) ([7d2c9be](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7d2c9be9d81174d255ac971c55626b89bacdd041))

## [1.3.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.7&targetVersion=GTv1.3.0-beta.8&_a=files) (2024-12-16)


### Bug Fixes

* **API:** latest API services generated, refers [#14207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14207) ([ce35965](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ce359652844bf97bdd934e21487e681b141ab2ae))

## [1.3.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.6&targetVersion=GTv1.3.0-beta.7&_a=files) (2024-12-16)


### Features

* **BFR Fee:** added fees creation, edit, view and logs, refers [#14124](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14124), [#13984](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13984) ([09f8429](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/09f8429b86aba7d6d2ca88a4dcdd25f2881858dd))


### Bug Fixes

* **Master Clients:** implement proper permissions for master client and STR fees, refers [#14143](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14143), [#14177](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14177) ([92902e6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/92902e615e30f8911ceb3bd80085440d6266d22a))

## [1.3.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.5&targetVersion=GTv1.3.0-beta.6&_a=files) (2024-12-13)


### Bug Fixes

* update API services, refers [#14143](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14143) ([597212c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/597212c29b9cb3321b816ca7f6c0bcf4c6175161))

## [1.3.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.4&targetVersion=GTv1.3.0-beta.5&_a=files) (2024-12-13)

## [1.3.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.3&targetVersion=GTv1.3.0-beta.4&_a=files) (2024-12-13)


### Bug Fixes

* fix tabs not displaying properly for fees, refers [#14143](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14143) ([d2c0efd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d2c0efd3a4925ee2e794a368cba7a50f1db5327f))

## [1.3.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.2&targetVersion=GTv1.3.0-beta.3&_a=files) (2024-12-13)

## [1.3.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.1&targetVersion=GTv1.3.0-beta.2&_a=files) (2024-12-13)


### Features

* **BFR Export submissions Data:** created view to export submissions data, added route to download, added table, refers [#14061](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14061) ([e8d3492](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e8d349206b19553e43a59c3ee99e1d6af88731bc))
* **STR Submission:** added export button to download the submission reports based on filters, features [#14140](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14140) ([841afa8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/841afa80eb55bfae99d11d68ed911a0d2721c012))

## [1.3.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.3.0-beta.0&targetVersion=GTv1.3.0-beta.1&_a=files) (2024-12-13)


### Features

* **Authorization:** added BFR module authorization, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([5e339b8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5e339b860b1a316a7801ea13f4365cce43aa8650))
* **BFR Search Submission:**  search and review submissions module, refers [#14076](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14076) ([9727847](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/97278479b7b77f28e489aac831577ced20dd10d7))


### Bug Fixes

* update masterclient trident user permissions, refers [#14142](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14142) ([e27e4d2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e27e4d24078a47346111ecb6a9394949134cc55b))

## [1.3.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0&targetVersion=GTv1.3.0-beta.0&_a=files) (2024-12-12)


### Features

* **Authorization:** add authorization logic, refers [#13989](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13989) ([c5931a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c5931a299bf364822c797bd78cd609d2a1126c96))
* **Permissions:** implement authorization for companies module, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([2481a25](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2481a25bf88d5eb2e87bd47cf781bb2df04e899f))
* **Permissions:** implement authorization to data migration module, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([0754cf4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0754cf43146d22f96b067ddf7a4d119a2f00506f))
* **Permissions:** implement authorization to user module, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([febd76b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/febd76b7081a1bcc3104d46f97317878ddeceda4))
* **Permissions:** implement authorization, refers [#13993](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13993) ([56a841f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/56a841f3f9121f6aeca532fceb889fb65ac848e9))
* **Permissions:** implement Master Client module authorization rules, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([3053d1c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3053d1c26caac452499c68e1b05f4a9549d5164e))
* **Permissions:** implement missing BO Directors authorization, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([7971717](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7971717ecf393951b86ec79fe64a9e9b4a0bad7b))
* **Permissions:** implement Reports module authorization, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([4e4a7fd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4e4a7fd8f42b932c761d5bd9f31d0612b0a9b8d0))
* **Permissions:** implement STR module authorization rules, refers [#11818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11818) ([13830ce](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/13830ce19b210908f3eeada678c694f943e96951))


### Bug Fixes

* correct cookie header handling on logout, fixes [#14123](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14123) ([38146fe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/38146fe6845567ce976d6d658023febd65e99395))

## [1.2.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.2&targetVersion=GTv1.2.0&_a=files) (2024-12-10)

## [1.2.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.1&targetVersion=GTv1.2.0-beta.2&_a=files) (2024-12-10)

## [1.2.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.0&targetVersion=GTv1.2.0-beta.1&_a=files) (2024-12-10)


### Features

* **BFR Financial Reports:** added page to list and download submissions, refers [#14022](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14022) ([750676f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/750676f7da314cf332e9899baa4c70360de78928))
* **BFR Payments:** added view to check the unpaid submissions, added modal to update the submissions paid status, refers [#14002](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14002), [#14003](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14003) ([00b3e49](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/00b3e49d4853296dd62aa10a488151bfd0ba6cbb))

## [1.2.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.1&targetVersion=GTv1.2.0-beta.0&_a=files) (2024-12-10)


### Features

* **BO/Directors:** add BO/Dir details, refers [#13965](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13965) ([b08e996](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b08e99684ada0c9456b18186110533ec3841b850))


### Bug Fixes

* **Company:** update "Status" into "State", refers [#14008](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14008) ([e0e4b46](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e0e4b46c77572123b43143bec319c93dcbe36ed1))
* **Simplified Tax Return:** delete unused fields in summary pdf, refers [#14069](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14069) ([767a017](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/767a0174aadfef6dad4c3ad079a853d67d682ce9))
* **Simplified Tax Return:** rename "Company representative" to "Registered agent", refers [#14066](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14066) ([fbca30f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/fbca30f3960ac39298ff37afe677e06358e23c0c))
* **STR Fees:** update value saved as description, refers [#14068](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14068) ([da4f911](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/da4f9117796201c01894babc16b6b455a3c16822))

## [1.1.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.1-beta.1&targetVersion=GTv1.1.1&_a=files) (2024-12-05)

## [1.1.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.1-beta.0&targetVersion=GTv1.1.1-beta.1&_a=files) (2024-12-05)

## [1.1.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0&targetVersion=GTv1.1.1-beta.0&_a=files) (2024-12-05)


### Bug Fixes

* **Alerts:** fix alerts not unsetting, refers [#13806](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13806) ([44a635f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/44a635ff9e45cf40a9baaf5a1b3a44293c4d4e2d))
* **Bo Directors:** table headers are now sortable, fix user preferences cookie, refers [#14005](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14005), [#13951](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13951) ([adcbbf4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/adcbbf499f426769e0c809d63be4846d5c0109a7))
* **BO/Directors:** fix bo/dirs overview wrong column mapping, refers [#13970](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13970) ([0edf9d2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0edf9d25ceba5d762fd5129636701b82669d560c))
* improve error handling and adds missing sorting column STR, refers [#13974](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13974), [#14006](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14006) ([b8512d5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b8512d5b302ea0baa5b801456f75f0a1ccc96d07))
* **Invoices:** add missing data to PDF and fix buttons not being disabled for STR actions, refers [#13923](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13923), [#13932](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13932) ([353677c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/353677c3edbeef724519740bba8728f3886ca1b3))
* **Late fee:** add proper validation for year selected, refers [#14004](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14004) ([d574f8c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d574f8cf397ddc408d3bb7edda921f268e5a0736))
* make application behavior visible with error logging, refers [#13983](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13983) ([6af7652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6af76529866d72ef3b84dd3f2e5bf95eb56aad89))
* **Simplified Tax Return:** implement sortable table headers, refers [#14006](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14006) ([cddd117](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/cddd11739c1b94c115c02bdd9a364f10294f5e59))
* **User preferences:** fix table pagesize not being parsed correctly, refers [#13951](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13951) ([4bd77c5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/4bd77c53a32b451f0acc48a1c7c816a2c87ccc04))

## [1.1.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.15&targetVersion=GTv1.1.0&_a=files) (2024-12-03)

## [1.1.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.14&targetVersion=GTv1.1.0-beta.15&_a=files) (2024-12-03)

## [1.1.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.13&targetVersion=GTv1.1.0-beta.14&_a=files) (2024-12-03)


### Bug Fixes

* **Bulk import:** update validation error where was expected to send two data rows, refers [#13829](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13829) ([82233af](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/82233af26e5891b8a2c326b39abfd9357ee2e209))
* **Bulk import:** updated route naming in payment import steps, refers [#13829](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13829) ([90983d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/90983d824e03d178fb77e9ba20d1e1afc1f1c487))
* **Simplified Tax Return:** show years after incorporation date for first submission, refers [#13814](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13814) ([065bdf6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/065bdf63735a22191cd88861837768975ec4a82a))

## [1.1.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.12&targetVersion=GTv1.1.0-beta.13&_a=files) (2024-12-02)

## [1.1.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.11&targetVersion=GTv1.1.0-beta.12&_a=files) (2024-12-02)


### Bug Fixes

* notifications did not check for correct type, refers [#12454](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12454) ([e3fa129](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e3fa129d08de3809724271aa1e171b617a0b3c21))

## [1.1.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.10&targetVersion=GTv1.1.0-beta.11&_a=files) (2024-12-02)


### Bug Fixes

* columns not being sortable on IRD overview, refers [#13748](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13748) ([b19b336](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b19b33613b1a43502fcad9d0125dd96042e4f614))
* hydration errors in EnhancedTable sheet, refers [#13687](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13687) ([5889125](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/58891250343b92afa964ca00bf874c36da09668d)), closes [/github.com/shadcn-ui/ui/issues/1104#issuecomment-2041219465](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/issuecomment-2041219465)
* **Master Clients:** hydration errors in browser console, refers [#13687](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13687) ([07c53c8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/07c53c8f67208734cee68024743d04c6d2815594))
* notification behavior, refers [#13222](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13222) ([1ae9d1d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1ae9d1d41726e68385bc024f2cc74c043994f058))
* **Users:** Update 2FA confirmation description and delete displayName from details, refers [#13813](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13813) ([252920b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/252920b69ec9ff24c354e496ed91240168caadeb))

## [1.1.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.9&targetVersion=GTv1.1.0-beta.10&_a=files) (2024-11-29)


### Bug Fixes

* **Simplified Tax Return:** add STR fee validations, refers [#13746](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13746) ([5393eb1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5393eb12159f921ea07d74f39c62994cc41b0e1f))

## [1.1.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.8&targetVersion=GTv1.1.0-beta.9&_a=files) (2024-11-29)


### Bug Fixes

* **Simplified Tax Return:** update the validation, refers [#13747](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13747) ([e88f6b5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e88f6b5940b019627832f479a47fbc921287ca4f))

## [1.1.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.7&targetVersion=GTv1.1.0-beta.8&_a=files) (2024-11-29)


### Features

* **Audit Logs:** implement back button for all audit log views, refers [#13669](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13669) ([eb3d72a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/eb3d72ae672e73ae54b66aa5dedca9ab42415cb2))
* **Companies:** customize first submission year upon approval and edit for approved companies, refers [#13651](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13651) ([a7e7d69](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a7e7d69e7179243ed9c8f0e714c82fd139103fc2))
* **Menu items:** renamed menu items for consistency, disabled unfinished menu items, refers [#13730](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13730), [#13749](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13749), [#13752](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13752) ([7358f0b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7358f0b582b10bb2d3324d88809049d6bb7c4cd9))


### Bug Fixes

* **Session age:** expire logged in sessions after 2 hours, refers [#13163](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13163), [#13638](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13638) ([ec31093](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ec3109397f567dfa4e4031547b4dd74560ffa778))

## [1.1.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.6&targetVersion=GTv1.1.0-beta.7&_a=files) (2024-11-28)


### Bug Fixes

* **Companies:** company decline modal and display decline action always, fixes [#13728](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13728) ([af3e053](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/af3e053e8047bd8eb35c93fe51cf44b5cb16a708))
* **IRD Export:** add proper error handling for file downloads, refers [#13666](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13666) ([2bc6c5c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2bc6c5c5ab620b2658351120830235cd99cd1ad4))
* **Master Clients:** adding removing manager email multiple times resolved, refers [#13531](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13531) ([41494db](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/41494db1c05da2974382c735c6da85e89ca76dc6))
* **Simplified Tax Return:** mapping of company name on STR summary document, fixes [#13739](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13739) ([f46b3c0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f46b3c034e9e4cd6f00446dd87d0f0860f8e9f07))
* STR error handler, refers [#13662](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13662) ([abe9925](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/abe9925019427c639fbec5c3bebbf848a2bfa2c8))
* type errors in bo directors overview page, refers [#13729](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13729) ([525c8df](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/525c8df423791e9f080848baff6403ecb6012663))

## [1.1.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.5&targetVersion=GTv1.1.0-beta.6&_a=files) (2024-11-28)

## [1.1.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.4&targetVersion=GTv1.1.0-beta.5&_a=files) (2024-11-28)


### Features

* **Invoices:** Add invoice PDF, refers [#13043](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13043) ([2fa1207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2fa1207ce3265a4d8b05b43942e9d5a82995f9e4))


### Bug Fixes

* **Companies:** fix company enable on account management approval, refers [#13650](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13650) ([fc6f0c6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/fc6f0c65bad29b6117dbcbbac94cd964b99643c6))
* EnhancedTable columns typedef, refers [#13724](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13724) ([67f08b6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/67f08b6b3823cbbcd86f986901957c14035d695c))

## [1.1.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.3&targetVersion=GTv1.1.0-beta.4&_a=files) (2024-11-28)


### Bug Fixes

* **Companies and Account management:** many bugs resolved in one PR, refers [#13242](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13242), [#13244](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13244), [#13245](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13245), [#13256](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13256), [#13436](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13436), [#13438](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13438), [#13526](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13526) ([f7d5355](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f7d53552cdec6ee94c7028fa938672f4a20a24ec))

## [1.1.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.2&targetVersion=GTv1.1.0-beta.3&_a=files) (2024-11-27)

## [1.1.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.1&targetVersion=GTv1.1.0-beta.2&_a=files) (2024-11-27)


### Bug Fixes

* **Master Client overview:** load master client from API instead of filtered dataset, refers [#13600](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13600) ([0e9bfcc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0e9bfccda7f8993e8c1aa564d2576d1836e0c485))

## [1.1.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0-beta.0&targetVersion=GTv1.1.0-beta.1&_a=files) (2024-11-27)

## [1.1.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.1-beta.1&targetVersion=GTv1.1.0-beta.0&_a=files) (2024-11-27)


### Features

* remix dev tools, refers [#13143](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13143) ([0749063](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/07490632d9d5022653b0171852a9f24f0d1bc12a))


### Bug Fixes

* FormSelect components not being resettable, refers [#13668](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13668) ([25922c6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/25922c6b038c92cb8ff26eb3c7e3642d5977d60e))
* remove save update button from user side sheet, refers [#13665](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13665) ([0674beb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0674bebb58549f2a00b5d10b26c9ea11f8600d13))

## [1.0.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.1-beta.0&targetVersion=GTv1.0.1-beta.1&_a=files) (2024-11-26)

## [1.0.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0&targetVersion=GTv1.0.1-beta.0&_a=files) (2024-11-26)


### Bug Fixes

* **Master Client module:** account for the possibility that the code is not defined, refers [#13600](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13600) ([8be6ed1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8be6ed1d839f60ec9d25a24b84d66ec97d481648))
* **Users overview:** disable extra columns, refers [#13658](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13658) ([a24cc33](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a24cc33b0eb198c3473752fe613851d217eb3812))

## [1.0.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.35&targetVersion=GTv1.0.0&_a=files) (2024-11-26)

## [1.0.0-beta.35](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.34&targetVersion=GTv1.0.0-beta.35&_a=files) (2024-11-26)

## [1.0.0-beta.34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.33&targetVersion=GTv1.0.0-beta.34&_a=files) (2024-11-26)


### Features

* **BO/Dir module:** Bo/Directors overview, refers [#13192](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13192), [#13193](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13193), [#13194](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13194) ([801dd67](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/801dd671f4b54d3c45430c451c84197e831751e4))
* **CI:** implement UAT deployment pipeline for management portal, refers [#13659](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13659) ([7aa8ef6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7aa8ef6ce606e6edbdf564b67caec6a873c1601f))
* **Data Migration:** add page for data migration, refers [#13356](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13356) ([26c5d57](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/26c5d57ac7bc0f943d273408780c58f349646c4c))
* **Reports:** overview page, refers [#13195](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13195), [#13196](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13196), [#13197](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13197), [#13201](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13201) ([303038c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/303038cba279f677c265633e5c055bc497915e84))

## [1.0.0-beta.33](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.32&targetVersion=GTv1.0.0-beta.33&_a=files) (2024-11-26)


### Bug Fixes

* **Auth:** implement middleware on unprotected routes, redirect users that aren't logged in, refers [#13637](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13637) ([2307e63](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2307e635eab25ba7b040fb6b2bf6651a48980b2f))

## [1.0.0-beta.32](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.31&targetVersion=GTv1.0.0-beta.32&_a=files) (2024-11-26)


### Features

* **Simplified Tax Return Fees:** STR Fee and late fee management, refers [#13041](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13041), [#13042](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13042) ([56990e6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/56990e6ca10352c7b67eda5fc465f017584604d0))
* **STR Financial Reports:** Created overview, download button, export to pdf functionality, refers [#13187](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13187), [#13039](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13039) ([f6e0fbb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f6e0fbb8d187ee0be35d5fe6c9fa819f76648698))


### Bug Fixes

* pages not rendering when initial userPreferences cookie is not set, refers [#13530](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13530) ([a1f6ec9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a1f6ec90ea4c69ac262476186a3cee95f94986e5))
* **STR Financial Reports:** added error handling and headers whitelist to improve the security when downloading financial reports, refers [#13595](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13595), [#13596](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13596) ([a9226b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a9226b96e30ecf00dea72539af7d31d57a74e03e))

## [1.0.0-beta.31](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.30&targetVersion=GTv1.0.0-beta.31&_a=files) (2024-11-22)

## [1.0.0-beta.30](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.29&targetVersion=GTv1.0.0-beta.30&_a=files) (2024-11-22)


### Features

* **Simplified Tax Return export:** add submission excel export, refers [#11311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11311), [#11309](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11309), [#13033](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13033), [#13034](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13034), [#13032](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13032), [#12760](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12760) ([2f7ca83](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2f7ca83e01ad068584eb162c56580aee54103c0c))

## [1.0.0-beta.29](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.28&targetVersion=GTv1.0.0-beta.29&_a=files) (2024-11-22)


### Features

* **API:** globally set x-userid header for api requests, refers [#13433](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13433) ([0c788e4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0c788e4c247d4bb4f12f581c58948f4777f9c3f1))
* implement consistent components, filtering logic, multiple consistency updates, refers [#13433](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13433), [#13479](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13479) ([966360f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/966360f70227d2801d143b058d841605c231266c))
* **Payment Management:** added table, added filters, added functionality to mark submissions as paid/unpaid, created bulk import pages and stepper, refers [#13198](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13198), [#13199](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13199), [#13200](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13200), [#13125](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13125), [#13126](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13126), [#13127](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13127), [#13128](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13128) ([d95c931](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d95c931caaa2e74cfcdc5bb8e63a99c13150c1b1))
* **Simplified Tax Return:** add STR submission download and reset to saved, refers [#13036](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13036), [#13037](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13037) ([d2b01c8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d2b01c8145c199b573b185382a56cb77064158c8))


### Bug Fixes

* @hey-api/openapi-ts should be a dev dependency, fixes [#13482](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13482) ([f2e5a74](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f2e5a74a68c9f55da975d63ed405403f42ba2546))
* **Companies module:** overlay implements updated api endpoints, refers [#13544](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13544) ([6738094](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/673809435b00b97a88a2e2405cac88356ade7f76))
* **Companies:** resolve acocunt-management overlay issues, user feedback, refers [#13518](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13518) ([425d901](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/425d901f829fc43607da81a4a7b9687a7755532f))
* **Master Clients:** implement API changes, fix user flows, refers [#13517](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13517), [#13209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13209) ([a1d248c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a1d248c351b53181b76d32a736e9c86486f0ef2f)), closes [#13531](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13531)
* move user menu under master clients, refers [#13523](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13523) ([dba65b8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/dba65b88b3101a4dc58e7a2e2fa6a42f34170e8c))
* unclearable form element, refers [#13449](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13449) ([7b062c4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7b062c435eda9e9bf510ae02363bc9fbc8cffdd2))
* **Users module:** implement correct layout components and filtering, refers [#13547](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13547) ([8b435b6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8b435b6fdb9dd9eb18a68defc7fd95166d7ef5af))

## [1.0.0-beta.28](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.27&targetVersion=GTv1.0.0-beta.28&_a=files) (2024-11-18)


### Features

* **BO Directors module:** add BO/Directors overview UI, refers [#13191](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13191) ([3a26507](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3a2650716054731d03db5431df0e7c878e880ab1))
* **Simplified Tax Return:** add UI for STR submissions, refers: [#13035](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13035) ([87acf08](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/87acf085bc528985510397722bd3aac6f0e7ee88))


### Bug Fixes

* update API generator command, make it compatible with Windows user, refers [#13405](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13405) ([2641d31](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2641d31fa7d3d3edb619d31f18a2faafbdc422e4))

## [1.0.0-beta.27](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.26&targetVersion=GTv1.0.0-beta.27&_a=files) (2024-11-15)

## [1.0.0-beta.26](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.25&targetVersion=GTv1.0.0-beta.26&_a=files) (2024-11-14)


### Features

* **Companies:** add email sending logic to management module, refers [#12090](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12090), [#12968](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12968) ([beae7ca](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/beae7caed94d53806255fc1ae18d19fca1b8d499))


### Bug Fixes

* confirmation dialogs focus, refers [#13276](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13276) ([6468e38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6468e386c421af8156d3d8552940c6cb6f826595))

## [1.0.0-beta.25](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.24&targetVersion=GTv1.0.0-beta.25&_a=files) (2024-11-13)

## [1.0.0-beta.24](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.23&targetVersion=GTv1.0.0-beta.24&_a=files) (2024-11-13)


### Features

* **Users module:** implement API for user management, add API service generator functions, refers [#12941](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12941), [#12972](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12972), [#12975](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12975), [#12978](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12978), [#13270](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13270) ([a245191](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a245191f2e19837262e22831ddecb80a8affe877))


### Bug Fixes

* cleanup code style and a bugfix, fixes [#13271](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13271) ([b7ae091](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b7ae0918744a814f726f72c7191f9a1421e2838a))
* hydration errors and breadcrumb generation, fixes [#13143](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13143), [#13142](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13142) ([14d40f1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/14d40f1c0401e180a154a246ac1c23532285710f))

## [1.0.0-beta.23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.22&targetVersion=GTv1.0.0-beta.23&_a=files) (2024-11-13)

## [1.0.0-beta.22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.21&targetVersion=GTv1.0.0-beta.22&_a=files) (2024-11-13)


### Features

* **Companies module:** add API logic to company module actions, refers [#11663](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11663), [#11667](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11667), [#12901](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12901), [#12959](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12959), [#12962](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12962), [#12969](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12969), [#12970](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12970), [#12983](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12983), [#12986](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12986), [#13052](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13052), [#13058](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13058), [#13164](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13164) ([0be8643](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0be86430756809802a95079d1700f7790b776290))
* **Users module:** implement API in users overview and detail, refers [#12941](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12941) ([f1a28d2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f1a28d231c3d8c761fcde5b43aacf880fcb1cc4a))


### Bug Fixes

* **API:** pass userId to API requests, fixes [#13152](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13152), [#13151](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13151) ([7c7371b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7c7371bbf588d294e16da0f53113d15b6654e127))
* **Companies:** apply active filter and proper implementation of generic components, refers [#13055](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13055) ([2a052b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2a052b935886a7a5d30e26f38ed49099fc8d09f0))
* **Companies:** fetch a single company instead of all companies when opening the overlay, fixes [#13231](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13231) ([122cd5d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/122cd5d988466270b452757ffebcebc5828110ad))
* **ESlint:** resolve eslint errors, closes [#13144](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13144) ([0acd044](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0acd044ce0fe2cd4b68b77fec151fff965f5cf40)), closes [#13206](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13206) [#13209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13209)

## [1.0.0-beta.21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.20&targetVersion=GTv1.0.0-beta.21&_a=files) (2024-11-07)

## [1.0.0-beta.20](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.19&targetVersion=GTv1.0.0-beta.20&_a=files) (2024-11-07)


### Bug Fixes

* remove debugging alert view from root layout, fixes [#13154](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13154) ([f158173](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f1581734fc860ad9c35608aba54d44f19c0d6de0))

## [1.0.0-beta.19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.18&targetVersion=GTv1.0.0-beta.19&_a=files) (2024-11-07)

## [1.0.0-beta.18](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.17&targetVersion=GTv1.0.0-beta.18&_a=files) (2024-11-07)

## [1.0.0-beta.17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.16&targetVersion=GTv1.0.0-beta.17&_a=files) (2024-11-07)


### Features

* **Companies module:** implement various fixes and refactors to make companies module compatible with new code, fixes [#13062](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13062) ([c1044e8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c1044e85d60a8cf5fda1d69c5bd81fccbcd37c35))
* **Master Client module:** implement all functionality to invite master client users, refers [#11607](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11607), [#11611](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11611), [#12904](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12904), [#13045](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13045), [#13046](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13046) ([3524e72](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3524e72daaedb003078eff27466793dbfeccb6c2))
* **Master Client module:** implement API endpoint for master client manager invitations, refers [#13120](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13120), [#13121](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13121) ([e6353cd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e6353cd45f50eb2bb653057b9a26123ef9cebbaf)), closes [#13122](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13122)
* **Users module:** implement history log view for users, refers [#12883](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12883) [#13116](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13116) [#13117](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13117) [#13118](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13118) ([646ac23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/646ac23c5d67cd059fcc25f9c4f7c179a49e548d))
* **Users module:** reset 2fa method, refers [#12976](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12976) ([a0e0d23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/a0e0d23db6fadfbed976fd22484932b2bc11521e))

## [1.0.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.15&targetVersion=GTv1.0.0-beta.16&_a=files) (2024-11-07)

## [1.0.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.14&targetVersion=GTv1.0.0-beta.15&_a=files) (2024-11-07)


### Bug Fixes

* install required dependencies that got lost in an earlier merge, fixes [#13149](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13149), [#13145](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13145) ([0cf164a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0cf164a616d30441a213cec14b6b06f40b25818f))

## [1.0.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.13&targetVersion=GTv1.0.0-beta.14&_a=files) (2024-11-07)


### Bug Fixes

* **ESlint:** updated to the latest version of @netpro/eslint-config, fixes [#13145](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13145) ([0a9db3f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0a9db3f19fc7ad1df09d0d4064b0edea63595e01))
* **Pagination component:** pageSize and pageNumber properties are handled internally now, fixes [#13140](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13140) ([2b34b8e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2b34b8eac8d517c166903096edea7d8fdcda6717))

## [1.0.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.12&targetVersion=GTv1.0.0-beta.13&_a=files) (2024-11-07)


### Bug Fixes

* updated npm package-lock.json due to bug in CI, fixes [#13145](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13145) ([84c9dd5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/84c9dd588732fab394c90bf238abf5eb34f7ef0e))

## [1.0.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.11&targetVersion=GTv1.0.0-beta.12&_a=files) (2024-11-07)


### Features

* **Companies module:** companies data is now retrieved from the API, refers [#11616](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11616), [#11662](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11662), [#12914](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12914), [#12925](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12925) ([f3990af](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/f3990afacf8d283e8b57fd9ea7eacc455995d4ef))
* **Companies module:** implement log view for individual companies, refers [#11666](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11666), [#12936](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12936), [#13114](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13114), [#13115](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13115) ([c7b745d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c7b745dd1bc0daa6410172fcc1f2f58463bcd23e))

## [1.0.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.10&targetVersion=GTv1.0.0-beta.11&_a=files) (2024-11-05)


### Features

* **Master Client module:** implement log view for master client audit logs, refers [#11609](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11609), [#11612](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11612), [#13105](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13105), [#12912](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12912), [#13049](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13049), [#13050](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13050), [#11614](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11614) ([78c9ce7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/78c9ce7ed31c724b0b4b51e18b49cef4ca7b5d81))
* **User Management:** implement static interface, [#12967](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12967), [#12973](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12973) ([c9db3f0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c9db3f0516841b88d16d328be738e1cbccf388a8))

## [1.0.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.9&targetVersion=GTv1.0.0-beta.10&_a=files) (2024-11-04)

## [1.0.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.8&targetVersion=GTv1.0.0-beta.9&_a=files) (2024-11-04)


### Features

* **Companies module:** company action, request update and send attention required email, refers: [#12090](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12090) ([3425237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/34252378047b14edefbbd3f985211a6a79f3ec9a))
* **Master Client module:** implement master client overview and manager account management, refers: [#11609](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11609), [#11612](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11612), [#13044](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13044), [#13047](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13047) ([3b35e9f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/3b35e9f43c5ab52ff9e11775e2ddf59a9689ec5d))

## [1.0.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.7&targetVersion=GTv1.0.0-beta.8&_a=files) (2024-11-04)


### Bug Fixes

* **Authentication:** redirect to login if user is not authenticated, fixes [#13026](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13026) ([146add6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/146add6204948c20cbd761f57cb14b32d8eaedcc))

## [1.0.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.6&targetVersion=GTv1.0.0-beta.7&_a=files) (2024-11-04)


### Bug Fixes

* **Authentication:** redirect to login if user is not authenticated, fixes [#13026](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13026) ([0217b2e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0217b2e4658224fae832716da22aafda1d5ecd53))

## [1.0.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.5&targetVersion=GTv1.0.0-beta.6&_a=files) (2024-11-02)


### Features

* **User Management:** users overview table, [#12902](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12902) ([d6cb0fc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d6cb0fccf81da6c2fe00d29c23b88b1373ab7d11))


### Bug Fixes

* **Authentication:** resolve issue where auth token refresh triggered a new login, [#12996](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12996) ([bc188e7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/bc188e7bad9e4c8bdd31483e70ccfbf8d67bece4))

## [1.0.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.4&targetVersion=GTv1.0.0-beta.5&_a=files) (2024-11-01)


### Features

* **CI:** release pipeline for UAT ([664bcda](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/664bcda73f0c3d1b47e002eec02596b74616c99a))

## [1.0.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.3&targetVersion=GTv1.0.0-beta.4&_a=files) (2024-11-01)


### Bug Fixes

* **Entra:** correct variable name for Entra redirect ([1986162](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/19861622686aef673bab39ef5fd4d21b7ddc8d32))

## [1.0.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.2&targetVersion=GTv1.0.0-beta.3&_a=files) (2024-11-01)


### ⚠ BREAKING CHANGES

* use standardized environment variable names

### Bug Fixes

* use standardized environment variable names ([c28ebdc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c28ebdcd43426ac20b38824af1d4573e4543ae09))

## [1.0.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.0.0-beta.1&targetVersion=GTv1.0.0-beta.2&_a=files) (2024-11-01)


### Features

* **CI:** add test deploy pipeline ([1b72598](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1b72598af2622660f3b61b9fa690c5cf5735f36f))


### Bug Fixes

* **NPM:** reinstalled packages due to npm CI error ([c2e7652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c2e76527415b3122dc36ead89b43090507f5e791))

## 1.0.0-beta.1 (2024-11-01)


### Features

* account management page ([1d755d5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1d755d5d239b7f5fec26321dc9818ef20ac045b0))
* add enhanced table component cookie endpoint, [#12950](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12950) ([17e319f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/17e319f7bdc7b31f9cf4f9de8fe6a6dd16c4b850))
* add search on email [#11612](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11612) ([35ca616](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/35ca6167a62c5467b19da6050e9604445d6fe99f))
* added enhanced table component ([7e30c9b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/7e30c9bc033719941c4a79d01f47d20044a1410e))
* added enhanced table component ([caa8ecc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/caa8ecc0aa624669326227c59c1b0f4c718b8af6))
* approval and decline buttons with fake actions [#12091](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12091) ([09a4b70](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/09a4b709b597398178090fbb73cc92192d13d47f))
* **Auth:** add MS Entra, closes [#12139](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12139) ([0466076](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/04660764bb49b84781f815940712fafe9bc6fc75))
* **Auth:** implement authentication and security middleware [#12933](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12933) ([e32d94f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/e32d94f7ac0fd59b193bab6d09653cab5d40c56c))
* **Changelog:** setup pipeline ([2fa2b96](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2fa2b968dfd44bb05684cc30f1513192ec212d0a))
* **CI:** implement dev deploy pipeline label ([2f310cf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2f310cf3d9292d55b3890bca964c27ad758aefc8))
* company overview and account management pages, related [#11616](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11616), [#11618](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11618) ([8973153](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8973153c3e1ecf48ea72d66c991783bad5a46ed2))
* company overview page ([fc780bd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/fc780bd453e7c09883314a5ddbf222d43319d9ae))
* company overview page, reusable components ([cdb8394](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/cdb8394783db18668a7609cd818a7fc5e625441b))
* create generic filtering components ([2d3d78d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2d3d78d85a09601151ccb0bb0214ced4b15f1475))
* create hook for easy search param manipulation ([c94e787](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c94e787628bfeb705fb60272ac1d8c9dabe031dd))
* **Design System:** set up design system for the application, closes [#12121](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12121) ([eeb2f87](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/eeb2f87921d6bee5259ff7abb38a45873ace2748))
* edit master client sheet, accordion ([623cb1e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/623cb1e2a5bb314fad503b18e16c7319da3514d4))
* **Enhanced Table:** add enhanced table component, refers [#12902](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12902) ([236584b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/236584b9348107abf5201840bb9303ddd1fdba9e))
* generic page error component ([1efc23c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1efc23c2c42e5d08dcb640a474972b6ab814fd8d))
* global error handling placeholder ([5f927a4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/5f927a488c9bde73ae62f39271e9332e2c37b9b0))
* implement authentication and session types ([8ea708a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8ea708aa2c77d40412b69244a4ccc0fd63faa15e))
* implement authentication middleware ([673bc1c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/673bc1ced6e073028d9cea68cfbae931c220041c))
* implement user profile and logout button ([ea4d27f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/ea4d27feda302fbe68c0c6d93c9281bc59a48d1c))
* initial setup of routes and layouts, closes [#12786](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12786) ([c603084](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c603084b79fc1e970878af002afeee151befddc2))
* install design system ([663e68d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/663e68dd1c046110989183a796057c1f42d688c2))
* install lucide react ([d4f3a45](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d4f3a450acf1ad79d58942ee1adaf6f7050fa5c9))
* landing page ([dc50dfe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/dc50dfed659e06a55462b34ff689cf219d15a681))
* master client search ([6f88c70](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/6f88c70f87adae7ecfb96d44ec09a7dfaf2ac56e))
* **Master Clients:** add static master client views, refers [#11609](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11609), [#11612](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11612) ([dbce604](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/dbce6048fe0690be69e766d68ecae865be11c8c5))
* **Pipeline:** add automated testing pipeline ([1d25b10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/1d25b102be0f49e2db761972dc5d1f5b9919c04a))
* **Pipeline:** setup eslint validation pipeline ([8a33a56](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8a33a56d8fb7218e99439ca83d50b83af58d274a))
* **setup:** add initial project set up ([85ae659](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/85ae6591ba2d7d55a81886b0bcbf5bda485444ea))
* **Setup:** add initial project set up ([bc67c48](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/bc67c481f639f5e6a98bc18381f50d383e57d737)), closes [#12102](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12102)


### Bug Fixes

* after logout, go back to landing page ([277f607](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/277f607b055c03ea99943c4cf9bf4baa6dac89b2))
* correct credentials ([12ad671](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/12ad671fe223987a0071e368840ac6ad95ae23e8))
* correct import ([b8c8f37](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/b8c8f37072a0f5065a1dcbe592ab34c80c354605))
* **eslint:** fix eslint issues ([00e1210](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/00e121072d54805c26bfa50912bf7b87bcfce0d0))
* if user is logged in, redirect to dashboard ([2e95eff](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/2e95effc5c3d0e8fb62bddd822f563ae9ee5b763))
* implement ScrollArea ([c619035](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/c619035bdd29b6ebb161d5e236669d5bebe9b76b))
* login page landing ([8bd2660](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/8bd2660a72d308550b4001f1d744088ea53216e0))
* missing favicon files ([d676e09](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/d676e09964392a9714344ae268d469edff4e2acd))
* redirect when on the root company route ([9c2056c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/9c2056c88f043ed660ee5b9aa8bcf6b1bf7ac8ee))
* use full height on body ([0ccb14d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/0ccb14de29cf64a5a8f20d9dff725daf0e1889f8))
