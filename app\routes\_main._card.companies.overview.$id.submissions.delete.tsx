import type { Row } from "@tanstack/react-table";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>ton, Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, Separator, Textarea } from "@netpro/design-system";
import { useLoaderData, useNavigate, useParams } from "@remix-run/react";
import { AlertTriangle } from "lucide-react";
import { useState } from "react";
import { getValidatedFormData } from "remix-hook-form";
import { EnhancedTable } from "~/components/EnhancedTable";
import { useDeleteSubmissionsColumns } from "~/features/companies/hooks/useDeleteSubmissionsColumns";
import { deleteSubmissionsSchema, type DeleteSubmissionsSchemaType } from "~/features/companies/schemas/delete-submissions";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { SubmissionDTO } from "~/services/api-generated";
import { getCompanyById } from "~/services/api-generated";

// TODO: Replace with actual API call to get submissions for this company
const mockSubmissions: SubmissionDTO[] = [
  {
    id: "1",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2019,
    status: "Draft" as any,
    createdAt: "2019-03-15T10:30:00Z",
    submittedAt: null,
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2019",
  },
  {
    id: "2",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2020,
    status: "Submitted" as any,
    createdAt: "2020-03-15T10:30:00Z",
    submittedAt: "2020-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2020",
  },
  {
    id: "3",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2021,
    status: "Submitted" as any,
    createdAt: "2021-03-15T10:30:00Z",
    submittedAt: "2021-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2021",
  },
  {
    id: "4",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2022,
    status: "Submitted" as any,
    createdAt: "2022-03-15T10:30:00Z",
    submittedAt: "2022-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2022",
  },
  {
    id: "5",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2023,
    status: "Submitted" as any,
    createdAt: "2023-03-15T10:30:00Z",
    submittedAt: "2023-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2023",
  },
  {
    id: "6",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2024,
    status: "Submitted" as any,
    createdAt: "2024-03-15T10:30:00Z",
    submittedAt: "2024-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2024",
  },
  {
    id: "7",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2025,
    status: "Submitted" as any,
    createdAt: "2025-03-15T10:30:00Z",
    submittedAt: "2025-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2025",
  },
  {
    id: "8",
    name: "Biffco Enterprises Ltd.",
    financialYear: 2026,
    status: "Submitted" as any,
    createdAt: "2026-03-15T10:30:00Z",
    submittedAt: "2026-04-01T14:20:00Z",
    legalEntityName: "Biffco Enterprises Ltd.",
    legalEntityCode: "BEC2026",
  },
];

export const loader = makeEnhancedLoader(async ({ params, request, json }) => {
  await middleware(["auth"], request);

  const { id: companyId } = params;
  if (!companyId) {
    throw new Response("Company not found", { status: 404 });
  }

  // Verify company exists
  const company = await getCompanyById({ headers: await authHeaders(request), path: { companyId } })
    .then(res => res.data);

  if (!company) {
    throw new Response("Company not found", { status: 404 });
  }

  // TODO: Replace with actual API call to get submissions for this company
  const submissions = mockSubmissions;

  return json({
    company,
    submissions,
  });
}, {
  authorize: ["companies.view"], // TODO: Add proper permission for deleting submissions
});

export const action = makeEnhancedAction(async ({ request, params, json, redirect, setNotification }) => {
  await middleware(["auth"], request);

  const { errors } = await getValidatedFormData<DeleteSubmissionsSchemaType>(
    request,
    zodResolver(deleteSubmissionsSchema),
  );

  if (errors) {
    return json({ errors });
  }

  const { id: companyId } = params;
  if (!companyId) {
    throw new Response("Company ID is required", { status: 400 });
  }

  /*
   * TODO: Implement actual API call to delete submissions
   * console.log("Deleting submissions:", data.submissionIds, "Reason:", data.reason);
   */

  setNotification({
    title: "Success",
    message: "Submissions deleted successfully",
    variant: "success",
  });

  return redirect(`/companies/overview/${companyId}/edit`);
}, {
  authorize: ["companies.view"], // TODO: Add proper permission for deleting submissions
});

export default function DeleteSubmissionsRoute() {
  const { submissions } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const params = useParams();
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [rowsSelected, setRowsSelected] = useState<Record<string, boolean>>({});
  const columns = useDeleteSubmissionsColumns();
  const handleClose = () => {
    navigate(`/companies/overview/${params.id}/edit`);
  };
  const handleDelete = () => {
    const selectedIds = Object.keys(rowsSelected).filter(id => rowsSelected[id]);

    if (selectedIds.length === 0) {
      return;
    }

    setConfirmOpen(true);
  };
  const handleRowClick = (row: Row<SubmissionDTO>) => {
    setRowsSelected((prev) => {
      const copy = { ...prev };

      row.getIsSelected() ? delete copy[row.id] : (copy[row.id] = true);

      return copy;
    });
  };
  const selectedCount = Object.keys(rowsSelected).filter(id => rowsSelected[id]).length;

  return (
    <>
      <Dialog open onOpenChange={() => {}}>
        <DialogContent className="max-w-4xl [&>button]:hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-6 h-6 text-red-600" />
              Delete Submissions
            </DialogTitle>
            <DialogDescription className="ml-8">
              Select below the submissions to be deleted.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* REMOVE ALL NAVIGATION FROM TABLE */}
            <EnhancedTable
              data={submissions}
              rowId="id"
              onRowClick={handleRowClick}
              reactTableOptions={{
                state: {
                  rowSelection: rowsSelected,
                },
                onRowSelectionChange: setRowsSelected,
              }}
              columns={columns}
              totalItems={submissions.length}
              showPagination={false}
            />

            <div className="p-4 rounded-lg">
              <h4 className="font-semibold text-sm mb-2">Note</h4>
              <ul className="text-sm space-y-1 list-disc list-inside">
                <li>Deleted submissions will no longer be visible for client and</li>
                <li>This action cannot be reversed</li>
              </ul>
            </div>
            <Separator />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={selectedCount === 0}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <DialogContent className="[&>button]:hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              Delete Submissions
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the selected submission(s) and invoice(s)?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label htmlFor="reason" className="block text-sm font-medium mb-2">
                Please specify why you want to delete the submissions*
              </label>
              <Textarea
                id="reason"
                placeholder="Enter reason for deletion..."
                rows={4}
              />
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => {
                setConfirmOpen(false);
              }}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
