import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import { formatDate } from "~/lib/utilities/format";
import type { ListSubmissionRFIDTO, ManagementListRfiSubmissionsData } from "~/services/api-generated";

type SortableColumns = NonNullable<NonNullable<ManagementListRfiSubmissionsData["query"]>["SortBy"]>;

// TODO: Update sortable columns to match RFI API
export const sortableColumnNames = Object.keys({
  LegalEntityName: null,
  LegalEntityCode: null,
  MasterClientCode: null,
  Status: null,
  FinancialPeriodEndsAt: null,
  ExportedAt: null,
  RFICreatedAt: null,
  RFIDeadLine: null,
  RFICompletedAt: null,
  RFILastReminderSentAt: null,
} satisfies Record<SortableColumns, null>)
const makeColumn = makeMakeColumn<SortableColumns, ListSubmissionRFIDTO>(sortableColumnNames)

export function useRfiColumns() {
  const formatColDate = useFormatColDate();
  const columns = [
    makeColumn({ header: "Entity Name", id: "legalEntityName", accessorKey: "legalEntityName" }),
    makeColumn({ header: "Regulatory Code", id: "legalEntityCode", accessorKey: "legalEntityCode" }),
    makeColumn({ header: "Master Client Code", id: "masterClientCode", accessorKey: "masterClientCode" }),
    makeColumn({ header: "Financial Period End Date", id: "financialPeriodEndsAt", accessorKey: "financialPeriodEndsAt", cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }) }), // UTC since it's a date only
    makeColumn({ header: "RFI Status", id: "rfiStatus", accessorKey: "rfiStatus", cell: data => data.row.original.rfiStatus }),
    makeColumn({ header: "Export Date", id: "exportedAt", accessorKey: "exportedAt", cell: formatColDate("exportedAt", { timezone: "UTC" }) }), // TODO: Check timezone (jurisdiction based/UTC)
    makeColumn({ header: "RFI Start Date", id: "rfiCreatedAt", accessorKey: "rfiCreatedAt", cell: formatColDate("rfiCreatedAt", { timezone: "UTC" }) }), // TODO: Check timezone (jurisdiction based/UTC)
    makeColumn({ header: "RFI Deadline Date", id: "rfiDeadLine", accessorKey: "rfiDeadLine", cell: (props) => {
      const { rfiDeadLine } = props.row.original;
      const isOverdue = rfiDeadLine ? new Date(rfiDeadLine) < new Date() : false;

      return (
        <span className={isOverdue ? "text-red-500" : ""}>{formatDate(rfiDeadLine, { timezone: "UTC" })}</span>
      );
    } }),
    makeColumn({ header: "Overdue", id: "overdue", accessorKey: "overdue", cell: (props) => {
      const { rfiDeadLine } = props.row.original;
      const isOverdue = rfiDeadLine ? new Date(rfiDeadLine) < new Date() : false;

      return (
        <span className={isOverdue ? "text-red-500" : ""}>{isOverdue ? "Yes" : "No"}</span>
      );
    } }),
    makeColumn({ header: "RFI Completed Date", id: "rfiCompletedAt", accessorKey: "rfiCompletedAt", cell: formatColDate("rfiCompletedAt", { timezone: "UTC" }) }), // TODO: Check timezone (jurisdiction based/UTC)
    makeColumn({ header: "Last Reminder Date", id: "rfiLastReminderSentAt", accessorKey: "rfiLastReminderSentAt", cell: formatColDate("rfiLastReminderSentAt", { timezone: "UTC" }) }), // TODO: Check timezone (jurisdiction based/UTC)
  ];

  return { columns }
}
