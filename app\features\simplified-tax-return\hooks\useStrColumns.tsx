import { Checkbox } from "@netpro/design-system";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListSubmissionDTO } from "~/services/api-generated";

type Type = "submissions" | "ird-export"
/**
 * This is a helper hook for the table column definitions.
 */
export function useStrColumns(type: Type) {
  const formatColDate = useFormatColDate()
  const columnHelper = createColumnHelper<ListSubmissionDTO>();
  let columns = [];
  let sortableColumns: string[] = [];
  const sortingEnabled = (column: string): boolean => sortableColumns.map(col => col.toLowerCase()).includes(column.toLowerCase());

  switch (type) {
    case "submissions":
      /**
       * Sortable columns as per API definition:
       * Available values : LegalEntityName, LegalEntityCode, LegalEntityVPCode, MasterClientCode, Status, FinancialYear, CreatedAt, ExportedAt, PaymentMethod, PaymentReceivedAt, PaymentReference
       */
      sortableColumns = [
        "CreatedByEmail",
        "LegalEntityName",
        "LegalEntityCode",
        "LegalEntityVPCode",
        "MasterClientCode",
        "Status",
        "FinancialYear",
        "IncorporationNr",
        "CreatedAt",
        "SubmittedAt",
        "ExportedAt",
        "PaymentMethod",
        "PaymentReceivedAt",
        "PaymentReference",
        "LegalEntityReferralOffice",
        "IsPaid",
        "LegalEntityVPStatus",
        "IsDeleted",
      ]

      columns = [
        columnHelper.accessor("createdByEmail", { header: "Email", id: "createdByEmail", enableSorting: sortingEnabled("createdByEmail") }),
        columnHelper.accessor("legalEntityName", { header: "Entity Name", id: "legalEntityName", enableSorting: sortingEnabled("legalEntityName") }),
        columnHelper.accessor("legalEntityCode", { header: "Regulatory Code", id: "legalEntityCode", enableSorting: sortingEnabled("legalEntityCode") }),
        columnHelper.accessor("legalEntityVPCode", { header: "VP Code", id: "legalEntityVPCode", enableSorting: sortingEnabled("legalEntityVPCode") }),
        columnHelper.accessor("legalEntityVPStatus", { header: "VP Entity Status", id: "legalEntityVPStatus", enableSorting: sortingEnabled("legalEntityVPStatus") }),
        columnHelper.accessor("masterClientCode", { header: "Master Client Code", id: "masterClientCode", enableSorting: sortingEnabled("masterClientCode") }),
        columnHelper.accessor("incorporationNr", { header: "Incorporation Number", id: "incorporationNr", enableSorting: sortingEnabled("incorporationNr") }),
        /*
         * Duplicate of legalEntityCode, but with a different header. Removed for now to allow for sortable headers.
         * columnHelper.accessor("legalEntityCode", { header: "VP Code", id: "legalEntityCode" }),
         */
        columnHelper.accessor("status", { header: "Status", id: "status", enableSorting: sortingEnabled("status") }),
        columnHelper.accessor("isDeleted", {
          header: "Is Paid?",
          id: "isPaid",
          enableSorting: sortingEnabled("isPaid"),
          cell: props => props.row.original.isPaid ? "Yes" : "No",
        }),
        columnHelper.accessor("isDeleted", {
          header: "Is Deleted?",
          id: "isDeleted",
          enableSorting: sortingEnabled("isDeleted"),
          cell: props => props.row.original.isDeleted ? "Yes" : "No",
        }),
        columnHelper.accessor("createdAt", {
          header: "Date Created",
          id: "createdAt",
          enableSorting: sortingEnabled("createdAt"),
          cell: formatColDate("createdAt", { timezone: "Nevis" }),
        }),
        columnHelper.accessor("submittedAt", {
          header: "Submitted Date",
          id: "submittedAt",
          enableSorting: sortingEnabled("submittedAt"),
          cell: formatColDate("submittedAt", { timezone: "Nevis" }),
        }),
        columnHelper.accessor("paymentReceivedAt", {
          header: "Paid Date",
          id: "paymentReceivedAt",
          enableSorting: sortingEnabled("paymentReceivedAt"),
          cell: formatColDate("paymentReceivedAt", { timezone: "Nevis" }),
        }),
        columnHelper.accessor("paymentReference", { header: "Payment Ref", id: "paymentReference", enableSorting: sortingEnabled("paymentReference") }),
        columnHelper.accessor("financialYear", { header: "Financial Year", id: "financialYear", enableSorting: sortingEnabled("financialYear") }),
        columnHelper.accessor("legalEntityReferralOffice", { header: "Referral Office", id: "legalEntityReferralOffice", enableSorting: sortingEnabled("legalEntityReferralOffice") }),
      ];
      break;
    case "ird-export":
      columns = [
        columnHelper.display({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        columnHelper.display({
          id: "entityName",
          header: "Entity Name",
          cell: props => props.row.original.legalEntityName,
        }),
        columnHelper.display({
          id: "entityNumber",
          header: "Regulatory Code",
          cell: props => props.row.original.legalEntityCode,
        }),
        columnHelper.display({
          id: "vpCode",
          header: "VP Code",
          cell: props => props.row.original.legalEntityVPCode,
        }),
        columnHelper.display({
          id: "masterClientCode",
          header: "Master Client Code",
          cell: props => props.row.original.masterClientCode,
        }),
        columnHelper.display({
          id: "status",
          header: "Status",
          cell: props => props.row.original.status,
        }),
        columnHelper.display({
          id: "financialPaymentYear",
          header: "Financial Year",
          cell: props => props.row.original.financialYear,
        }),
        columnHelper.display({
          id: "created",
          header: "Date Created",
          cell: formatColDate("createdAt", { timezone: "Nevis" }),
        }),
        columnHelper.display({
          id: "exportDate",
          header: "Export Date",
          cell: formatColDate("exportedAt", { timezone: "Nevis" }),
        }),
        columnHelper.display({
          id: "paymentMethod",
          header: "Payment Method",
          cell: props => props.row.original.paymentMethod,
        }),
        columnHelper.display({
          id: "datePaid",
          header: "Paid Date",
          cell: formatColDate("paymentReceivedAt", { timezone: "Nevis" }),
        }),
        columnHelper.display({
          id: "paymentRef",
          header: "Payment Ref",
          cell: props => props.row.original.paymentReference,
        }),
      ]
  }

  return { columns }
}
