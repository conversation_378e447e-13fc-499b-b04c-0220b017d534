import type { JurisdictionWithModules } from "../types/jurisdiction";
import { getJurisdictionModules, getJurisdictionsForUser } from "~/services/api-generated";
import { authHeaders } from "../../../lib/auth/utils/auth-headers";

export async function getUserJurisdictions(request: Request, userId: string): Promise<JurisdictionWithModules[]> {
  const headers = await authHeaders(request);
  const { data: availableJurisdictions, error: jurisdictionsError } = await getJurisdictionsForUser({ headers, path: { userId } });

  if (jurisdictionsError) {
    throw new Error("Failed to fetch user jurisdictions");
  }

  const jurisdictionModules = await Promise.all(
    availableJurisdictions
      .filter(jurisdiction => jurisdiction.id)
      .map(async jurisdiction => ({
        id: jurisdiction.id as string,
        name: jurisdiction.name as string,
        modules: (await getJurisdictionModules({ headers, path: { jurisdictionId: jurisdiction.id as string } })).data?.modules || [],
      })),
  );

  return jurisdictionModules;
}
