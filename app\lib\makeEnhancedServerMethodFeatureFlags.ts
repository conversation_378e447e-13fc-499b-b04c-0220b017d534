import { redirect } from "@remix-run/node";
import { commonGetFeatureFlags } from "~/services/api-generated";
import { authHeaders } from "./auth/utils/auth-headers";

type Args = {
  request: Request
  requiredFeatureFlags: string[]
}

export async function makeEnhancedServerMethodFeatureFlags({ request, requiredFeatureFlags }: Args): Promise<void> {
  const { data: featureFlags } = await commonGetFeatureFlags({
    headers: await authHeaders(request),
  });
  const enabledFlags = featureFlags || [];
  const hasAllRequiredFlags = requiredFeatureFlags.every(flagName =>
    enabledFlags.some(flag => flag.name === flagName && flag.isEnabled),
  );

  if (!hasAllRequiredFlags) {
    throw redirect("/404");
  }
}
