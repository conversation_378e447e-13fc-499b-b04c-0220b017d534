import type { ReactNode } from "react";
import type { FieldErrors } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Collapsible, CollapsibleContent, FormDescription, Input, Label, RadioGroup, RadioGroupItem, SelectItem, Switch, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Link, useActionData, useLoaderData, useParams } from "@remix-run/react";
import { parseISO } from "date-fns";
import { CircleAlert, ScrollText, Trash2 } from "lucide-react";
import { useContext, useEffect } from "react";
import { getValidatedFormData, useRemixForm } from "remix-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetContext } from "~/components/ActionSheetContext";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Authorized } from "~/components/Authorized";
import { Form } from "~/components/Form";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormInput } from "~/components/FormInput";
import { FormSelect } from "~/components/FormSelect";
import { FormSwitch } from "~/components/FormSwitch";
import type { EditCompanySchemaType } from "~/features/companies/schemas/edit-company";
import { editCompanySchema } from "~/features/companies/schemas/edit-company";
import type { JurisdictionSettingsResponse } from "~/features/jurisdictions/api/get-jurisdiction-settings";
import { useUserHasPermission } from "~/hooks/use-user-has-permission";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { Modules } from "~/lib/utilities/modules";
import { userHasPermission } from "~/lib/utilities/user-has-permission";
import type {
  CompanyAnnualFeesDTO,
  CompanyDTO,
  CompanyModuleDTO,
  ListCompanyModulesDTO,
  SettingsDTO,
} from "~/services/api-generated";
import {
  getCompanyAnnualFeeStatusByCompany,
  getCompanyById,
  getCompanyModules,
  getCompanySettings,
  getJurisdictionSettings,
  setCompanyAnnualFeeStatusByCompany,
  setCompanyModules,
  setCompanySettings,
} from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ params, request, json }) => {
  await middleware(["auth"], request);

  const { id: companyId } = params;
  if (!companyId) {
    throw new Response("Company not found", { status: 404 });
  }

  const company = await getCompanyById({ headers: await authHeaders(request), path: { companyId } })
    .then(res => res.data as CompanyDTO);

  if (!company) {
    throw new Response("Not Found", { status: 404 });
  }

  try {
    const [
      jurisdictionSettings,
      companySettings,
      companyModules,
      annualFees,
    ] = await Promise.all([
      getJurisdictionSettings({ headers: await authHeaders(request), path: { jurisdictionId: company.jurisdictionId as string } })
        .then(res => res.data as JurisdictionSettingsResponse),
      getCompanySettings({ headers: await authHeaders(request), path: { companyId } })
        .then(res => res.data as SettingsDTO),
      getCompanyModules({ headers: await authHeaders(request), path: { companyId } })
        .then(res => res.data as ListCompanyModulesDTO),
      getCompanyAnnualFeeStatusByCompany({ headers: await authHeaders(request), path: { companyId } })
        .then(res => res.data as CompanyAnnualFeesDTO),
    ]);

    return json({
      company,
      modules: companyModules.modules as CompanyModuleDTO[],
      companySettings,
      jurisdictionSettings,
      annualFees,
    });
  } catch (error) {
    console.error("Error fetching company-related data:", error);
    throw new Response("Failed to fetch company data", { status: 500 });
  }
}, {
  authorize: ["companies.view"],
});

export const action = makeEnhancedAction(async ({ request, params, getUserPermissions, json }) => {
  await middleware(["auth"], request);

  const permissions = await getUserPermissions();

  if (!permissions?.permissions) {
    throw new Response("Failed to get user permissions", { status: 403 });
  }

  // TODO: Add logic to save inactive company settings
  const canUpdateModules = userHasPermission(permissions.permissions, { oneOf: ["companies.modules.available.set"] });
  const canUpdateSTRFee = userHasPermission(permissions.permissions, { oneOf: ["companies.custom-str-fee.set"] });
  const canUpdateBFRFee = userHasPermission(permissions.permissions, { oneOf: ["companies.custom-bfr-fee.set"] });
  const canUpdateAnnualFees = userHasPermission(permissions.permissions, { oneOf: ["companies.annual-fee.set"] });
  // Validate the form data and return any errors.
  const { errors, data } = await getValidatedFormData<EditCompanySchemaType>(request, zodResolver(editCompanySchema));
  if (errors) {
    return json({ errors });
  }

  const { id: companyId } = params;

  if (!companyId) {
    throw new Response("Company ID is required", { status: 400 });
  }

  const company = await getCompanyById({ headers: await authHeaders(request), path: { companyId } })
    .then(res => res.data as CompanyDTO);

  if (!company) {
    throw new Response("Company not found", { status: 404 });
  }

  let feeSettingsUpdate: SettingsDTO = { feeSettings: {} };

  if (canUpdateSTRFee) {
    feeSettingsUpdate = {
      feeSettings: {
        ...feeSettingsUpdate.feeSettings,
        strSubmissionFee: data.strSubmissionFee,
        ...(data.latePaymentException !== undefined && {
          strSubmissionLatePaymentFeeExempt: data.latePaymentException === "true",
        }),
      },
    };
  }

  if (canUpdateBFRFee) {
    feeSettingsUpdate = {
      feeSettings: {
        ...feeSettingsUpdate.feeSettings,
        bfrSubmissionFee: data.bfrModule ? data.bfrSubmissionFee : undefined,
      },
    };
  }

  if (canUpdateAnnualFees && data.annualFees && data.annualFees.length) {
    const updateAnnualFees = await setCompanyAnnualFeeStatusByCompany({
      headers: await authHeaders(request),
      path: {
        companyId,
      },
      body: {
        annualFees: data.annualFees,
      },
    });

    if (!updateAnnualFees.response.ok) {
      return json({ errors: { annualFees: { message: "Failed to update annual fees" } } as FieldErrors<EditCompanySchemaType> });
    }
  }

  const submissionSettingsUpdate = canUpdateModules
    ? { submissionSettings: {
        firstSubmissionYear: data.firstSubmissionYear ? Number(data.firstSubmissionYear) : null,
      } }
    : {};
  const updateResult = await setCompanySettings({
    headers: await authHeaders(request),
    path: {
      companyId,
    },
    body: {
      ...submissionSettingsUpdate,
      ...feeSettingsUpdate,
    },
  });

  if (!updateResult.response.ok) {
    return json({ errors: { strSubmissionFee: { message: "Failed to update STR submission fees" } } as FieldErrors<EditCompanySchemaType> });
  }

  if (canUpdateModules) {
    const companyModules = await getCompanyModules({ headers: await authHeaders(request), path: { companyId } })
      .then(res => res.data as ListCompanyModulesDTO);

    if (!companyModules) {
      throw new Response("Company modules not found", { status: 404 });
    }

    const findModuleByKey = (key: string) => (companyModules.modules as CompanyModuleDTO[]).find(m => m.key === key);
    const strModule = findModuleByKey(Modules.SIMPLIFIED_TAX_RETURN);
    const boModule = findModuleByKey(Modules.BO_DIRECTORS);
    const bfrModule = findModuleByKey(Modules.BASIC_FINANCIAL_REPORT);
    const esBahamasModule = findModuleByKey(Modules.ECONOMIC_SUBSTANCE_BAHAMAS);
    const moduleUpdates = [];

    if (strModule) {
      moduleUpdates.push({
        id: strModule.id,
        isEnabled: data.strModule,
        isApproved: true,
      });
    }

    if (boModule) {
      moduleUpdates.push({
        id: boModule.id,
        isEnabled: data.boDirModule,
        isApproved: true,
      });
    }

    if (bfrModule) {
      moduleUpdates.push({
        id: bfrModule.id,
        isEnabled: data.bfrModule,
        isApproved: true,
      });
    }

    if (esBahamasModule) {
      moduleUpdates.push({
        id: esBahamasModule.id,
        isEnabled: data.esBahamasModule,
        isApproved: true,
      });
    }

    const moduleUpdateRequest = await setCompanyModules({
      headers: await authHeaders(request),
      path: {
        companyId,
      },
      body: {
        modules: moduleUpdates,
      },
    });
    if (!moduleUpdateRequest.response.ok) {
      return json({ errors: { strModule: { message: "Failed to update company modules" } } as FieldErrors<EditCompanySchemaType> });
    }
  }

  return json({
    success: true,
  });
}, {
  authorize: ["companies.custom-str-fee.set", "companies.modules.available.set", "companies.custom-bfr-fee.set", "companies.annual-fee.set"],
});

export default function EditCompanyRoute(): ReactNode {
  const formatDate = useFormatDate()
  const { closeSheet } = useContext(ActionSheetContext);
  const { company, companySettings, jurisdictionSettings, modules, annualFees } = useLoaderData<typeof loader>();
  const params = useParams();
  const isNevis = company.jurisdictionName === Jurisdictions.NEVIS;
  const isPanama = company.jurisdictionName === Jurisdictions.PANAMA;
  const isBahamas = company.jurisdictionName === Jurisdictions.BAHAMAS;
  const feeSettings = companySettings?.feeSettings as Record<string, number | boolean> || {};
  const actionData = useActionData<typeof action>();
  const simplifiedTaxReturnEnabled = modules.find(m => m.key === Modules.SIMPLIFIED_TAX_RETURN)?.isEnabled;
  const boDirectorsModuleEnabled = modules.find(m => m.key === Modules.BO_DIRECTORS)?.isEnabled;
  const bfrModuleEnabled = modules.find(m => m.key === Modules.BASIC_FINANCIAL_REPORT)?.isEnabled;
  const esBahamasModuleEnabled = modules.find(m => m.key === Modules.ECONOMIC_SUBSTANCE_BAHAMAS)?.isEnabled;
  const firstSubmissionYear = companySettings.submissionSettings?.firstSubmissionYear;
  const formMethods = useRemixForm<EditCompanySchemaType>({
    mode: "onSubmit",
    resolver: zodResolver(editCompanySchema),
    submitConfig: {
      action: `/companies/overview/${params.id}/edit`,
      method: "POST",
    },
    defaultValues: {
      strSubmissionFee: feeSettings?.strSubmissionFee as number | null,
      bfrSubmissionFee: feeSettings?.bfrSubmissionFee as number | null,
      latePaymentException: (feeSettings?.strSubmissionLatePaymentFeeExempt as boolean | null) === true
        ? "true"
        : (feeSettings?.strSubmissionLatePaymentFeeExempt as boolean | null) === false
            ? "false"
            : undefined,
      strModule: simplifiedTaxReturnEnabled ?? false,
      boDirModule: boDirectorsModuleEnabled ?? false,
      bfrModule: bfrModuleEnabled ?? false,
      esBahamasModule: esBahamasModuleEnabled ?? false,
      annualFees: annualFees?.annualFees || [],
      firstSubmissionYear: firstSubmissionYear === 0 || firstSubmissionYear === null ? undefined : firstSubmissionYear?.toString(),
      enableHistoricalFilings: false, // TODO: Get from company settings
      historicalFilingsDate: undefined, // TODO: Get from company settings
    },
  });
  const { formState: { errors, isSubmitting }, clearErrors, register, watch, setValue } = formMethods;

  useEffect(() => {
    if (actionData && "success" in actionData) {
      closeSheet()
    }
  }, [actionData, closeSheet])

  const strModuleWatch = watch("strModule")
  const bfrModuleWatch = watch("bfrModule")
  const esBahamasModuleWatch = watch("esBahamasModule")
  const enableHistoricalFilingsWatch = watch("enableHistoricalFilings")
  // Permissions
  const setModules = useUserHasPermission({ oneOf: ["companies.modules.available.set"] });
  const setStrFee = useUserHasPermission({ oneOf: ["companies.custom-str-fee.set"] });
  const setAnnualFee = useUserHasPermission({ oneOf: ["companies.annual-fee.set"] });
  // Generate years from 2019 to current year
  const submissionYears = Array.from({ length: new Date().getFullYear() + 1 - 2019 }, (_, i) => 2019 + i);
  const displayedYears = submissionYears?.filter(year => company?.incorporationDate ? year >= parseISO(company.incorporationDate).getFullYear() : true) ?? [];
  //
  const showSTR = ["IBC", "LLC"].includes(company.entityType || "")
  const hasAnnualFees = (isBahamas && esBahamasModuleWatch) || (isNevis && strModuleWatch);

  return (
    <Form formMethods={formMethods} remixFormProps={{ method: "post", className: "h-full" }}>
      <ActionSheetBody>
        <ActionSheetContent title="Edit Company">
          <ActionSheetSection title="Details">
            <ActionSheetDescriptionList
              data={{
                companyName: company?.name ?? "Unknown",
                incorporationNumber: company.incorporationNumber,
                incorporationDate: company?.incorporationDate ? formatDate(company.incorporationDate) : "N/A",
                masterClientCode: company.masterClientCode,
                companyNumber: company.code,
                vpEntityStatus: company.vpEntityStatus,
                referralOffice: company.referralOffice,
              }}
              headers={[
                ["companyName", "Entity Name"],
                ["companyNumber", "VP Code"],
                ["incorporationNumber", "Incorporation Number"],
                ["incorporationDate", "Incorporation Date"],
                ["masterClientCode", "Master Client Code"],
                ["vpEntityStatus", "VP Entity Status"],
                ["referralOffice", "Referral Office"],
              ]}
            />
          </ActionSheetSection>
          <Authorized oneOf={["companies.modules.available.view"]}>
            <ActionSheetSection title="Enabled Modules" subTitle="The selected modules are visible to the client.">
              <div className="flex flex-col gap-2 pr-1">
                {isNevis && showSTR && (
                  <Label htmlFor="strModule" className="flex justify-between w-full items-center cursor-pointer">
                    <span>
                      Simplified Tax Return
                    </span>
                    <Switch
                      id="strModule"
                      checked={strModuleWatch}
                      disabled={!setModules}
                      onCheckedChange={value => setValue("strModule", value)}
                      withIcon
                    />
                  </Label>
                )}
                {isPanama && (
                  <Label htmlFor="bfrModule" className="flex justify-between w-full items-center cursor-pointer">
                    <span>
                      Basic Financial Report
                    </span>
                    <Switch
                      id="bfrModule"
                      checked={bfrModuleWatch}
                      onCheckedChange={value => setValue("bfrModule", value)}
                      withIcon
                    />
                  </Label>
                )}
                {isBahamas && (
                  <Label htmlFor="esBahamasModule" className="flex justify-between w-full items-center cursor-pointer">
                    <span>
                      Economic Substance
                    </span>
                    <Switch
                      id="esBahamasModule"
                      checked={esBahamasModuleWatch}
                      onCheckedChange={value => setValue("esBahamasModule", value)}
                      withIcon
                    />
                  </Label>
                )}
                <Label htmlFor="boDirModule" className="flex justify-between w-full items-center cursor-pointer">
                  <span>
                    Ownership & Officers
                  </span>
                  <Switch
                    id="boDirModule"
                    checked={watch("boDirModule")}
                    disabled={!setModules}
                    onCheckedChange={value => setValue("boDirModule", value)}
                    withIcon
                  />
                </Label>
              </div>
            </ActionSheetSection>
          </Authorized>
          {isNevis && showSTR && (
            <ActionSheetSection title="Settings">
              <div className="space-y-4">
                <Authorized oneOf={["companies.custom-str-fee.view"]}>
                  <Collapsible open={strModuleWatch}>
                    <CollapsibleContent>
                      <div className="flex flex-col gap-1 mt-3 text-xs">
                        <Label className="text-sm font-medium" htmlFor="amount">Custom STR Submission Fee</Label>
                        <Input
                          disabled={isSubmitting || !setStrFee}
                          type="number"
                          {...register("strSubmissionFee")}
                          invalid={Boolean(errors?.strSubmissionFee)}
                        />
                        <FormDescription>
                          Default STR Submission fee: $
                          {(jurisdictionSettings?.feeSettings as any)?.strSubmissionFee ?? "N/A"}
                        </FormDescription>
                        {errors?.strSubmissionFee && <p className="text-red-600">{errors.strSubmissionFee.message}</p>}
                      </div>
                      {displayedYears && strModuleWatch && (
                        <FormSelect
                          name="firstSubmissionYear"
                          label="First Submission Year"
                          selectProps={{ disabled: !setModules }}
                          selectValueProps={{ placeholder: "Select a year" }}
                          options={displayedYears?.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                        />
                      )}
                    </CollapsibleContent>
                  </Collapsible>
                  <div className="flex flex-col gap-1 text-xs pl-1">
                    <Label className="text-sm font-medium">Apply late payment exception?</Label>
                    <RadioGroup
                      value={watch("latePaymentException")}
                      onValueChange={(value: "true" | "false") => setValue("latePaymentException", value)}
                      disabled={isSubmitting || !setStrFee}
                      className="flex space-x-4 mt-1 flex-row"
                    >
                      <div className="flex items-center gap-2">
                        <RadioGroupItem value="true" id="yes" />
                        <Label className="text-sm" htmlFor="yes">Yes</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <RadioGroupItem value="false" id="no" />
                        <Label className="text-sm" htmlFor="no">No</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </Authorized>
              </div>
            </ActionSheetSection>
          )}
          {isPanama && (
            <ActionSheetSection title="Settings">
              <div className="space-y-4">
                {/* TODO: When the API has a fee view for panama, wrap it inside the Authorized component */}
                <Collapsible open={bfrModuleWatch}>
                  <CollapsibleContent>
                    <div className="flex flex-col gap-1 mt-3 text-xs">
                      <FormInput name="bfrSubmissionFee" label="Basic Financial Report fee (USD)" inputProps={{ disabled: isSubmitting, type: "number" }} />
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </ActionSheetSection>
          )}
          {(!company.isActive && company.onboardingStatus === "Approved") && (
            <Authorized oneOf={["companies.annual-fee.view"]}>
              {/* TODO: Await for permission tbd */}
              <div>
                <div className="mb-2 text-blue-700 flex items-center">
                  <h4 className="text-xl font-semibold">Inactive Company Settings</h4>
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <CircleAlert className="flex shrink-0 size-4 ml-2" />
                    </TooltipTrigger>
                    <TooltipContent className="p-5 space-y-3 font-inter" side="top" align="end">
                      <p>
                        This will allow clients to file a submission even though the company is closed.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className="space-y-4">
                  <Collapsible open>
                    <CollapsibleContent>
                      <Label htmlFor="enableHistoricalFilings" className="flex justify-between w-full items-center cursor-pointer">
                        <span>
                          Enable historical filings
                        </span>
                        <Switch
                          id="enableHistoricalFilings"
                          checked={enableHistoricalFilingsWatch}
                          onCheckedChange={value => setValue("enableHistoricalFilings", value)}
                          withIcon
                        />
                      </Label>
                      {enableHistoricalFilingsWatch && (
                        <>
                          {/* TODO: Add correct permissions to disable forms */}
                          {!isNevis && (
                            <FormDatePicker
                              name="historicalFilingsDate"
                              label="Allow client to file till:"
                              datePickerProps={{
                                disabledDates: {
                                  before: company.incorporationDate ? new Date(company.incorporationDate) : undefined,
                                },
                              } as any}
                            />
                          )}
                          {isNevis && (
                            <FormSelect
                              name="historicalFilingsYear"
                              label="Allow client to file till:"
                              selectProps={{ disabled: !setModules }}
                              selectValueProps={{ placeholder: "Select a year" }}
                              options={displayedYears?.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                            />
                          )}
                        </>
                      )}
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              </div>
            </Authorized>
          )}
          {hasAnnualFees && annualFees?.annualFees?.length && (
            <Authorized oneOf={["companies.annual-fee.view"]}>
              <div>
                <div className="mb-2 text-blue-700 flex items-center">
                  <h4 className="text-xl font-semibold">Annual Fee (Paid)</h4>
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <CircleAlert className="flex shrink-0 size-4 ml-2" />
                    </TooltipTrigger>
                    <TooltipContent className="p-5 space-y-3 font-inter" side="top" align="end">
                      <p>
                        <span className="font-bold">Please note:</span>
                        {" "}
                        This change only affects future submissions! Historical submissions are not affected.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className="space-y-4">
                  <Collapsible open>
                    <CollapsibleContent>
                      <div className="grid grid-cols-2 gap-1 mt-3 text-xs">
                        {annualFees?.annualFees?.filter(fee => [2023, 2024, 2025].includes(fee.financialYear || 0))?.map((fee) => {
                          // Find the original index in the unfiltered array for form field naming
                          const originalIndex = annualFees?.annualFees?.findIndex(f => f.financialYear === fee.financialYear) ?? 0;

                          return (
                            <div key={fee.financialYear}>
                              <FormSwitch
                                name={`annualFees.${originalIndex}.isPaid`}
                                label={fee.financialYear?.toString() || "N/A"}
                                switchProps={{ withIcon: true, disabled: isSubmitting || !setAnnualFee }}
                                formItemProps={{ className: "flex justify-start space-x-2 w-full items-end cursor-pointer" }}
                              />
                            </div>
                          );
                        })}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              </div>
            </Authorized>
          )}
          <Authorized oneOf={["companies.log.view"]}>
            <ActionSheetSection title="Action">
              <ActionSheetActionRow label="History">
                <Button
                  asChild
                  size="sm"
                  variant="outline"
                  type="button"
                  className="self-center text-sm flex items-center justify-center gap-1.5 mr-2"
                >
                  <Link to={`/companies/overview/${params.id}/log`}>
                    <ScrollText size={14} className="text-blue-600" />
                    <span className="text-xs font-semibold">View Log</span>
                  </Link>
                </Button>
              </ActionSheetActionRow>
              <ActionSheetActionRow label="Delete Submissions">
                <Button
                  asChild
                  size="sm"
                  variant="outline"
                  type="button"
                  className="self-center text-sm flex items-center justify-center gap-1.5 mr-2"
                >
                  <Link to={`/companies/overview/${params.id}/submissions/delete`}>
                    <Trash2 size={14} className="text-blue-600" />
                    <span className="text-xs font-semibold">Delete</span>
                  </Link>
                </Button>
              </ActionSheetActionRow>
            </ActionSheetSection>
          </Authorized>
        </ActionSheetContent>
        <ActionSheetFooter>
          <Authorized oneOf={["companies.custom-str-fee.set", "companies.annual-fee.set", "companies.modules.available.set"]}>
            <Button disabled={isSubmitting} onClick={() => clearErrors()} type="submit">
              Save
            </Button>
          </Authorized>
        </ActionSheetFooter>
      </ActionSheetBody>
    </Form>
  );
}
