import type { ReactNode } from "react";
import { createContext, useMemo, useState } from "react";

import type { FeatureFlagDTO } from "~/services/api-generated";

export type FeatureFlag = FeatureFlagDTO;

export type FeatureFlags = {
  featureFlags: FeatureFlag[] | null
};

export type CreateFeatureFlagContextType = {
  initialFlags: FeatureFlags
};

export type FeatureFlagContextType = {
  flags: FeatureFlags
  setFlags: (value: FeatureFlags) => void
  isEnabled: (flagName: string) => boolean
};

export const FeatureFlagContext = createContext<FeatureFlagContextType | undefined>(undefined);

export function FeatureFlagProvider({
  initialFlags,
  children,
}: CreateFeatureFlagContextType & { children: ReactNode }): JSX.Element {
  const [flags, setFlags] = useState<FeatureFlags>(initialFlags);
  const value = useMemo(
    () => ({
      flags,
      setFlags,
      isEnabled: (flagName: string) => flags.featureFlags?.find(flag => flag.name === flagName)?.isEnabled ?? false,
    }),
    [flags],
  );

  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  );
}
