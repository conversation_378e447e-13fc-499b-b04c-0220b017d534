import type { ReactNode } from "react";

type DetailRowProps = {
  label: string
  children: ReactNode
}

export function DetailRow({ label, children }: DetailRowProps) {
  return (
    <div className="grid grid-cols-[200px_1fr] md:grid-cols-[200px_1fr] sm:grid-cols-1 gap-x-4">
      <div className="text-sm font-semibold text-gray-600">{label}</div>
      <div className="text-sm text-gray-800 min-w-0">{children}</div>
    </div>
  );
}
