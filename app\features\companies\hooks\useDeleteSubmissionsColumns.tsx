import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@netpro/design-system";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { ManagementListSubmissionsData, SubmissionDTO } from "~/services/api-generated";

type SortableColumns = NonNullable<NonNullable<ManagementListSubmissionsData["query"]>["SortBy"]>;

const sortableColumnNames = Object.keys({
  CreatedAt: null,
  CreatedByEmail: null,
  ExportedAt: null,
  FinancialYear: null,
  LegalEntityCode: null,
  LegalEntityName: null,
  LegalEntityVPCode: null,
  LegalEntityVPStatus: null,
  IncorporationNr: null,
  MasterClientCode: null,
  IsPaid: null,
  Status: null,
  SubmittedAt: null,
  PaymentMethod: null,
  PaymentReceivedAt: null,
  PaymentReference: null,
} satisfies Record<SortableColumns, null>);
const makeColumn = makeMakeColumn<SortableColumns, SubmissionDTO>(sortableColumnNames);

export function useDeleteSubmissionsColumns() {
  // TODO: Replace with correct type when API is ready
  const columns: ColumnDef<SubmissionDTO>[] = [
    makeColumn({
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
            || (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => {
            if (value) {
              table.toggleAllPageRowsSelected(true);
            } else {
              table.resetRowSelection(false);
            }
          }}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(Boolean(value))}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 50, // set a fixed width of 50px for the checkbox column
      maxSize: 50,
    }),
    makeColumn({
      id: "LegalEntityName",
      header: "Module",
      cell: () => "Biffco Enterprises Ltd.", // TODO: Replace with actual module name
    }),
    makeColumn({
      id: "FinancialYear",
      header: "Financial Year",
      accessorKey: "financialYear",
    }),
    makeColumn({
      id: "Status",
      header: "Status",
      accessorKey: "status",
    }),
  ];

  return columns;
}
