import { useLoaderData } from "@remix-run/react";
import type { TaxPayerIdentificationSchemaType } from "~/features/economic-substance-tbah/types/tax-payer-identification-schema";
import { transformNumberToCurrency } from "~/features/economic-substance-tbah/utilities/currencies";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import { transformBooleanStringToLabel } from "~/features/economic-substance-tbah/utilities/summary";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummarySubItem } from "./table/SummarySubItem";
import { SummaryTable } from "./table/SummaryTable";
import { SummaryTableData } from "./table/SummaryTableData";
import { SummaryTableRow } from "./table/SummaryTableRow";

export function TaxResidencyEntityIncome() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const {
    isBahamianResident,
    isInvestmentFund,
    entityGrossTotalAnnualIncomeCurrency,
    entityGrossTotalAnnualIncomeAmount,
    isPartOfMneGroup,
    mneGroupName,
    intendsToClaimTaxResidencyOutsideBahamas,
    taxResidencyJurisdiction,
    taxPayerIdentificationNumber,
  } = submissionData[Pages.TAX_PAYER_IDENTIFICATION] as TaxPayerIdentificationSchemaType

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Tax Residency and Entity Income</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow className={isBahamianResident === "true" ? "border-b-0" : ""}>
            <SummaryTableData>
              Are you a 100% Bahamian/resident owned, and Core Income
              Generated Activity (CIGA) conducted in The Bahamas?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(isBahamianResident)}</SummaryTableData>
          </SummaryTableRow>
          {isBahamianResident === "false" && (
            <SummaryTableRow>
              <SummaryTableData>
                <SummarySubItem>
                  Are you an Investment Fund according to the Investment Funds
                  Act, 2019 (No. 2 of 2019)?
                </SummarySubItem>
              </SummaryTableData>
              <SummaryTableData>{transformBooleanStringToLabel(isInvestmentFund)}</SummaryTableData>
            </SummaryTableRow>
          )}
          {isInvestmentFund === "false" && (
            <>
              <SummaryTableRow>
                <SummaryTableData>
                  Entity Gross total annual Income:
                </SummaryTableData>
                <SummaryTableData>
                  {`${transformNumberToCurrency(Number(entityGrossTotalAnnualIncomeAmount))} ${entityGrossTotalAnnualIncomeCurrency}`}
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className={isPartOfMneGroup === "true" ? "border-b-0" : ""}>
                <SummaryTableData>
                  Are you a part of the MNE Group?
                </SummaryTableData>
                <SummaryTableData>{transformBooleanStringToLabel(isPartOfMneGroup)}</SummaryTableData>
              </SummaryTableRow>
              {isPartOfMneGroup === "true" && (
                <SummaryTableRow>
                  <SummaryTableData>
                    <SummarySubItem>
                      Name of MNE Group:
                    </SummarySubItem>
                  </SummaryTableData>
                  <SummaryTableData>{mneGroupName}</SummaryTableData>
                </SummaryTableRow>
              )}
              <SummaryTableRow className={intendsToClaimTaxResidencyOutsideBahamas === "true" ? "border-b-0" : ""}>
                <SummaryTableData>
                  Does the entity intend to make a claim of tax residency outside of the
                  Bahamas?
                </SummaryTableData>
                <SummaryTableData>{transformBooleanStringToLabel(intendsToClaimTaxResidencyOutsideBahamas)}</SummaryTableData>
              </SummaryTableRow>
              {intendsToClaimTaxResidencyOutsideBahamas === "true" && (
                <>
                  <SummaryTableRow className="border-b-0">
                    <SummaryTableData>
                      <SummarySubItem>
                        Jurisdiction in which the entity is tax resident
                      </SummarySubItem>
                    </SummaryTableData>
                    <SummaryTableData>{taxResidencyJurisdiction}</SummaryTableData>
                  </SummaryTableRow>
                  <SummaryTableRow>
                    <SummaryTableData>
                      <SummarySubItem>
                        Tax Payer Identification Number (“TIN”) or other identification
                        reference number:
                      </SummarySubItem>
                    </SummaryTableData>
                    <SummaryTableData>{taxPayerIdentificationNumber}</SummaryTableData>
                  </SummaryTableRow>
                </>
              )}
            </>
          )}
        </tbody>
      </SummaryTable>
    </div>
  )
}
