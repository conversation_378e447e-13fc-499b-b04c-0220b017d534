import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { ListSubmissionDTO, ManagementListSubmissionsData } from "~/services/api-generated";

type SortableColumns = NonNullable<NonNullable<ManagementListSubmissionsData["query"]>["SortBy"]>;

export const sortableColumnNames = Object.keys({
  Status: null,
  CreatedAt: null,
  ExportedAt: null,
  SubmittedAt: null,
  PaymentMethod: null,
  FinancialYear: null,
  LegalEntityCode: null,
  LegalEntityName: null,
  PaymentReference: null,
  MasterClientCode: null,
  PaymentReceivedAt: null,
  LegalEntityVPCode: null,
  LegalEntityVPStatus: null,
  IncorporationNr: null,
  CreatedByEmail: null,
  IsPaid: null,
} satisfies Record<SortableColumns, null>)
const makeColumn = makeMakeColumn<SortableColumns, ListSubmissionDTO>(sortableColumnNames)

export function useBfrColumns(): { columns: ReturnType<typeof makeColumn>[] } {
  const formatColDate = useFormatColDate();
  const columns = [
    makeColumn({
      id: "isUsingAccountingRecordsTool",
      header: "Use Accounting Records?",
      accessorKey: "isUsingAccountingRecordsTool",
      cell: (props) => {
        return (
          <div>
            {props.row.original.isUsingAccountingRecordsTool === "true" ? "Yes" : "No"}
          </div>
        )
      },
      enableSorting: false, // This field is not in the sortable columns list
    }),
    makeColumn({
      id: "financialPeriodEndsAt",
      header: "Financial Period End Date",
      accessorKey: "financialPeriodEndsAt",
      cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }),
      enableSorting: false, // This field is not in the sortable columns list
    }),
    makeColumn({
      id: "LegalEntityName",
      header: "Entity Name",
      accessorKey: "legalEntityName",
    }),
    makeColumn({
      id: "LegalEntityCode",
      header: "Regulatory Code",
      accessorKey: "legalEntityCode",
    }),
    makeColumn({
      id: "MasterClientCode",
      header: "Master Client Code",
      accessorKey: "masterClientCode",
    }),
    makeColumn({
      id: "LegalEntityVPCode",
      header: "VP Code",
      accessorKey: "legalEntityVPCode",
    }),
    makeColumn({
      id: "Status",
      header: "Status",
      accessorKey: "status",
    }),
    makeColumn({
      id: "IsDeleted",
      header: "Is Deleted?",
      accessorKey: "IsDeleted",
      cell: data => data.row.original.isDeleted ? "Yes" : "No",
    }),
    makeColumn({
      id: "CreatedAt",
      header: "Created Date",
      accessorKey: "createdAt",
      cell: formatColDate("createdAt", { timezone: "Panama" }),
    }),
  ];

  return { columns }
}
