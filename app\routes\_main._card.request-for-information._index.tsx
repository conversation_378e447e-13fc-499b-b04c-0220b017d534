import { <PERSON><PERSON>, SelectItem } from "@netpro/design-system";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { Filter } from "lucide-react";
import { type ReactNode, useEffect, useMemo } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormCombobox } from "~/components/FormCombobox";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { relevantActivityOptions } from "~/features/economic-substance-tbah/types/relevant-activities";
import { getUserJurisdictions } from "~/features/jurisdictions/api/get-user-jurisdictions";
import { useRfiColumns } from "~/features/rfi/hooks/useRfiColumns";
import { searchSchema } from "~/features/rfi/schemas/rfi-search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { RequestForInformationStatus } from "~/services/api-generated";
import { managementListRfIs } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Request For Information",
    to: "/request-for-information",
  },
  title: "Request For Information",
}

export const loader = makeEnhancedLoader(async ({ json, request, getUserPreferences, enhancedURL }) => {
  const { tablePageSize } = await getUserPreferences()
  const { userId } = await middleware(["auth"], request);
  const { data: rfiData, error: rfiError } = await managementListRfIs({
    headers: await authHeaders(request),
    query: {
      PageNumber: Number(enhancedURL.searchParams.get("page")) || undefined,
      GeneralSearchTerm: enhancedURL.searchParams.get("search") ?? undefined,
      SortOrder: enhancedURL.searchParams.get("orderDirection") ?? undefined,
      SortBy: enhancedURL.searchParams.get("order") as any ?? undefined,
      JurisdictionId: enhancedURL.searchParams.get("jurisdictionId") || undefined,
      ModuleId: enhancedURL.searchParams.get("moduleId") || undefined,
      Status: enhancedURL.searchParams.get("status") as RequestForInformationStatus | undefined ?? undefined,
      IsOverdue: enhancedURL.searchParams.get("isOverdue") === "true" ? true : enhancedURL.searchParams.get("isOverdue") === "false" ? false : undefined,
      PageSize: tablePageSize,
    },
  })

  if (rfiError) {
    throw new Response("Failed to fetch RFI data", { status: 500 });
  }

  const availableJurisdictions = await getUserJurisdictions(request, userId);

  return json({
    rfiData,
    availableJurisdictions,
  })
}, { authorize: ["rfi.view"] })

export default function RequestForInformationLayout(): ReactNode {
  const navigate = useNavigate();
  const { rfiData, availableJurisdictions } = useLoaderData<typeof loader>()
  const { formMethods } = useFilterForm(searchSchema);
  const { columns } = useRfiColumns();
  const selectedJurisdiction = formMethods.watch("jurisdictionId");
  const rfiStatusOptions = [
    { key: "draft", label: "Draft" },
    { key: "active", label: "Active" },
    { key: "cancelled", label: "Cancelled" },
    { key: "completed", label: "Completed" },
  ];
  const jurisdictionOptions = availableJurisdictions.map(jurisdiction => (
    <SelectItem key={jurisdiction.id} value={jurisdiction.id}>
      {jurisdiction.name}
    </SelectItem>
  ));
  const moduleOptions = useMemo(() => (
    availableJurisdictions.find(j => j.id === selectedJurisdiction)?.modules.map(module => (
      <SelectItem key={module.id} value={module.id as string}>
        {module.name}
      </SelectItem>
    ))
  ), [selectedJurisdiction, availableJurisdictions]);

  useEffect(() => {
    if (selectedJurisdiction) {
      const modules = availableJurisdictions.find(j => j.id === selectedJurisdiction)?.modules || [];
      formMethods.setValue("moduleId", modules[0].id);
    } else {
      formMethods.setValue("moduleId", undefined);
    }
  }, [availableJurisdictions, formMethods, selectedJurisdiction]);

  return (
    <CardContainer>
      <Authorized oneOf={["rfi.view"]}>
        <Form formMethods={formMethods}>
          <FilterRow cols={5}>
            <FormColumnsFilter label="Visible Columns" columns={columns} />
            <FormSelect
              name="jurisdictionId"
              label="Jurisdiction"
              selectValueProps={{ placeholder: "Select jurisdiction" }}
              options={jurisdictionOptions}
            />
            <FormSelect
              name="moduleId"
              label="Module"
              selectValueProps={{ placeholder: "All" }}
              selectProps={{ disabled: !((moduleOptions ?? []).length > 1) }}
              options={moduleOptions || []}
            />
            <FormSelect
              name="status"
              label="RFI Status"
              selectValueProps={{ placeholder: "All" }}
              options={rfiStatusOptions.map(status => (
                <SelectItem key={status.key} value={status.key}>{status.label}</SelectItem>
              ))}
            />
            <FormSelect
              name="isOverdue"
              label="Overdue?"
              selectValueProps={{ placeholder: "All" }}
              options={[
                <SelectItem key="yes" value="true">Yes</SelectItem>,
                <SelectItem key="no" value="false">No</SelectItem>,
              ]}
            />
          </FilterRow>
          <FilterRow cols={5}>
            <div className="col-span-full">
              <FormCombobox
                name="relevantActivities"
                label="Relevant Activities"
                options={relevantActivityOptions}
                comboboxProps={{
                  placeholder: "Select an activity",
                  searchText: "Search...",
                  noResultsText: "No activities found.",
                  multiple: true,
                }}
              />
            </div>
          </FilterRow>
          <FilterRow>
            <div className="col-span-full flex flex-row items-center gap-2">
              <FormSearch
                name="search"
                formItemProps={{ className: "w-full" }}
                inputProps={{ placeholder: "Search entity name, entity number or master client code" }}
              />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
            </div>
          </FilterRow>
        </Form>
      </Authorized>

      <EnhancedTableContainer>
        <EnhancedTable
          rowId="id"
          columns={columns}
          data={rfiData?.data}
          totalItems={rfiData?.totalItemCount}
          onRowClick={row => navigate(`/request-for-information/${row.id}`)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  )
}
