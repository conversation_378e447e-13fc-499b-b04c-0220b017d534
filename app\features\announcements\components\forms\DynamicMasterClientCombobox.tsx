import type { ComponentProps } from "react";
import { Combobox } from "@netpro/design-system";
import { useCallback, useEffect, useState } from "react";
import { FormFieldReset } from "~/components/FormFieldReset";
import { makeFormField } from "~/lib/makeFormField";

type Props = {
  comboboxProps?: ComponentProps<typeof Combobox>
}

type DynamicComboboxInternalProps = {
  field: any
  fieldState: any
  comboboxProps?: ComponentProps<typeof Combobox>
}

/**
 * Internal component that handles the dynamic loading logic with hooks
 */
function DynamicComboboxInternal({ field, fieldState, comboboxProps }: DynamicComboboxInternalProps) {
  const [items, setItems] = useState<{ value: string, label: string }[]>([]);

  // Initialize items with current field values (for edit mode)
  useEffect(() => {
    if (field.value && Array.isArray(field.value) && field.value.length > 0) {
      const currentItems = field.value.map((code: string) => ({
        value: code,
        label: code,
      }));
      setItems(currentItems);
    }
  }, [field.value]);
  const onSearch = useCallback(async (query: string) => {
    // Clear items if query is empty
    if (!query) {
      setItems([]);

      return;
    }

    try {
      // Use the client-side API route to search master clients
      const response = await fetch(`/api/master-clients/search?searchTerm=${encodeURIComponent(query)}&pageSize=50`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.data) {
        setItems(result.data);
      } else {
        setItems([]);
      }
    } catch (err) {
      console.error("Error fetching master clients:", err);
      setItems([]);
    }
  }, []);

  return (
    <div className="relative">
      <Combobox
        invalid={!!fieldState.error}
        items={items}
        onChange={field.onChange}
        value={field.value}
        defaultValue={field.value}
        onSearch={onSearch}
        searchText="Start typing to search master clients"
        debounceDelay={500}
        shouldFilter={false}
        {...comboboxProps}
      />
      {field.value && !comboboxProps?.disabled && (
        <FormFieldReset variant="select" onReset={() => field.onChange(comboboxProps?.multiple ? [] : "")} />
      )}
    </div>
  );
}

/**
 * Combobox field for Master Clients with server-side search.
 *
 * Debounces input (500ms), uses server-side filtering (shouldFilter=false), and
 * initializes options from the current value in edit forms. Supports single and multi-select.
 *
 * @prop comboboxProps - Props forwarded to \@netpro/design-system Combobox (e.g., multiple, placeholder, disabled).
 */

export const DynamicMasterClientCombobox = makeFormField<Props>({
  displayName: "DynamicMasterClientCombobox",
  render: ({ field, fieldState, comboboxProps }) => {
    return <DynamicComboboxInternal field={field} fieldState={fieldState} comboboxProps={comboboxProps} />;
  },
});
