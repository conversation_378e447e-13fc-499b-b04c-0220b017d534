import type { ReactNode } from "react";
import { redirect } from "@remix-run/react";
import { CancelRFIDialog } from "~/features/rfi/components/dialogs/CancelRFIDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import type { CancelRequestForInformationDTO } from "~/services/api-generated";
import { managementCancelRfi } from "~/services/api-generated";

const routeName = "request-for-information"
export const loader = makeEnhancedLoader(async ({ json }) => {
  return json(null);
}, { authorize: ["rfi.cancel"] });

export const action = makeEnhancedAction(async ({ request, params, setNotification, json }) => {
  const { id } = params;
  if (!id) {
    setNotification({ title: "RFI ID is required", variant: "error" });

    return redirect(routeName);
  }

  const formData = await request.formData()
  const data = formData.get("data") as string
  const body = JSON.parse(data) as CancelRequestForInformationDTO;
  const { error } = await managementCancelRfi({ headers: await authHeaders(request), path: { requestForInformationId: id }, body })
  if (error) {
    setNotification({ title: "Failed cancelling RFI", variant: "error" })
  } else {
    setNotification({ title: "RFI cancelled", message: "RFI cancelled successfully", variant: "success" })
  }

  return json(null)
}, { authorize: ["rfi.cancel"] })

export default function CancelRFI(): ReactNode {
  return (<CancelRFIDialog routeName={routeName} />)
}
