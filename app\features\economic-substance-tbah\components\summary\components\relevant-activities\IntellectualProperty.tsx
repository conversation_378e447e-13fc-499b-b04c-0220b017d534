import { useLoaderData } from "@remix-run/react";
import type { IntellectualPropertySchemaType } from "~/features/economic-substance-tbah/types/intellectual-property-schema";
import { getCurrencyString } from "~/features/economic-substance-tbah/utilities/currencies";
import type { PageSlug } from "~/features/economic-substance-tbah/utilities/form-pages";
import { transformBooleanStringToLabel } from "~/features/economic-substance-tbah/utilities/summary";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableData } from "../table/SummaryTableData";
import { SummaryTableRow } from "../table/SummaryTableRow";

export function IntellectualProperty({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const {
    isHighRiskIpEntity,
    relevantIpAsset,
    incomeGenerationExplanation,
    employeeResponsibility,
    strategicDecisionsBahamas,
    tradingActivitiesBahamas,
    grossIncomeRoyalties,
    grossIncomeSaleIpAsset,
    grossIncomeOtherSources,
    businessPlanExplanation,
    decisionMakingEvidenceExplanation,
    additionalComplianceExplanation,
  } = submissionData[page] as IntellectualPropertySchemaType

  return (
    <div className="space-y-5">
      <h2 className="text-blue-500 font-thin mb-4 text-lg">7. Intellectual Property</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Is the entity a high-risk intellectual property entity?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(isHighRiskIpEntity)}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Identify the relevant intellectual property asset(s) held by the entity.
            </SummaryTableData>
            <SummaryTableData>{relevantIpAsset}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Explain how income is generated from the intellectual property asset(s).
            </SummaryTableData>
            <SummaryTableData>{incomeGenerationExplanation}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Identify the decisions for which each employee is responsible for in respect of the generation of income from the intellectual property.
            </SummaryTableData>
            <SummaryTableData>{employeeResponsibility}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Explain how strategic decisions are made in the Bahamas.
            </SummaryTableData>
            <SummaryTableData>{strategicDecisionsBahamas}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Explain how trading activities are conducted in the Bahamas.
            </SummaryTableData>
            <SummaryTableData>{tradingActivitiesBahamas}</SummaryTableData>
          </SummaryTableRow>
          {isHighRiskIpEntity === "true" && (
            <>
              <SummaryTableRow>
                <SummaryTableData>
                  Gross income through Royalties, if applicable.
                </SummaryTableData>
                <SummaryTableData>
                  {grossIncomeRoyalties ? getCurrencyString(grossIncomeRoyalties) : "N/A"}
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  Gross income through Gains from sale of IP asset, if applicable.
                </SummaryTableData>
                <SummaryTableData>
                  {grossIncomeSaleIpAsset ? getCurrencyString(grossIncomeSaleIpAsset) : "N/A"}
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  Gross income through Other sources, if applicable.
                </SummaryTableData>
                <SummaryTableData>
                  {grossIncomeOtherSources ? getCurrencyString(grossIncomeOtherSources) : "N/A"}
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  Provide detailed business plans which explain the commercial rationale of holding the intellectual property assets in the Bahamas.
                </SummaryTableData>
                <SummaryTableData>{businessPlanExplanation}</SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  Provide concrete evidence that decision-making is taking place within the Bahamas, including but not limited to, minutes of meetings which have taken place in the Bahamas.
                </SummaryTableData>
                <SummaryTableData>{decisionMakingEvidenceExplanation}</SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  Provide any additional information to demonstrate compliance with the economic substance requirements.
                </SummaryTableData>
                <SummaryTableData>{additionalComplianceExplanation}</SummaryTableData>
              </SummaryTableRow>
            </>
          )}
        </tbody>
      </SummaryTable>
    </div>
  )
}
