import type { AnnouncementSchemaType } from "../schema/announcement-schema";
import { fileDataToFile } from "~/lib/utilities/files";
import { formatDate, formatDateForAPI } from "~/lib/utilities/format";
import type { AnnouncementDTO } from "~/services/api-generated";

export function getDefaultValues(announcementData: AnnouncementDTO | null): Partial<AnnouncementSchemaType> {
  if (!announcementData) {
    return {
      subject: "",
      emailSubject: "",
      sendNow: undefined,
      scheduledDate: undefined,
      scheduledTime: undefined,
      sendToAllMasterClients: undefined,
      jurisdictionId: "",
      masterClientCodes: [],
      body: "",
    }
  }

  const { sendAt, sentAt, subject, emailSubject, jurisdictionIds, body, masterClientIds, masterClientCodes } = announcementData;
  const sendNowDefaultValue = sentAt === null ? "false" : "true"
  const scheduledTimeDefaultValue = sendAt ? formatDate(sendAt, { formatStr: "HH:mm" }) : undefined
  const scheduledDateDefaultValue = sendAt ? formatDateForAPI(sendAt, { short: true }) : undefined
  const sendToAllMasterClientsDefaultValue = !(masterClientIds && masterClientIds.length > 0) ? "true" : "false"
  // Jurisdiction Id logic
  let jurisdictionIdDefaultValue = ""
  if (jurisdictionIds && jurisdictionIds.length > 0) {
    if (jurisdictionIds.length === 1) {
      jurisdictionIdDefaultValue = jurisdictionIds[0]
    }

    if (jurisdictionIds.length > 1) {
      jurisdictionIdDefaultValue = "all"
    }
  }

  const fileArray: File[] = []
  if (announcementData.documents) {
    announcementData.documents.forEach((doc) => {
      const file = fileDataToFile(doc.document!)
      fileArray.push(file)
    });
  }

  return {
    subject: subject ?? "",
    emailSubject: emailSubject ?? "",
    sendNow: sendNowDefaultValue,
    scheduledDate: scheduledDateDefaultValue,
    scheduledTime: scheduledTimeDefaultValue,
    sendToAllMasterClients: sendToAllMasterClientsDefaultValue,
    jurisdictionId: jurisdictionIdDefaultValue,
    masterClientCodes: masterClientCodes ?? [],
    body: body ?? "",
    files: fileArray,
  }
}
