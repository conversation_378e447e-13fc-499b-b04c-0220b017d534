import { useLoaderData } from "@remix-run/react";
import { Fragment, type ReactNode } from "react";
import { Declaration } from "~/features/economic-substance-tbah/components/summary/components/Declaration";
import { EntityDetails } from "~/features/economic-substance-tbah/components/summary/components/EntityDetails";
import { FinancialPeriod } from "~/features/economic-substance-tbah/components/summary/components/FinancialPeriod";
import { getActivitySummary } from "~/features/economic-substance-tbah/components/summary/components/get-activity-summary";
import { ParentEntity } from "~/features/economic-substance-tbah/components/summary/components/ParentEntity";
import { RelevantActivities } from "~/features/economic-substance-tbah/components/summary/components/RelevantActivities";
import { SummaryPage } from "~/features/economic-substance-tbah/components/summary/components/SummaryPage";
import { SupportingDetails } from "~/features/economic-substance-tbah/components/summary/components/SupportingDetailst";
import { TaxIdentificationAddress } from "~/features/economic-substance-tbah/components/summary/components/TaxIdentificationAddress";
import { TaxResidencyEntityIncome } from "~/features/economic-substance-tbah/components/summary/components/TaxResidenctyEntityIncome";
import type { RelevantActivityDeclarationSchemaType } from "~/features/economic-substance-tbah/types/relevant-activity-declaration-schema";
import type { TaxPayerIdentificationSchemaType } from "~/features/economic-substance-tbah/types/tax-payer-identification-schema";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import { getUnflattenedDataSet } from "~/features/submissions/utilities/submission-data-set";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { SubmissionStatus } from "~/services/api-generated";
import { managementGetSubmission } from "~/services/api-generated";

export type EconomicSubstanceSummaryLoader = Promise<{
  submissionData: Record<string, any>
  entityDetails: {
    legalEntityName: string
    status: SubmissionStatus | undefined
    companyIdentityCode: string | undefined | null
    masterClientCode: string | undefined | null
    submittedAt: string | undefined | null
  }
}>
export const loader = makeEnhancedLoader(async ({ request, params }) => {
  await middleware(["auth"], request);
  const { id } = params;

  if (!id) {
    throw new Response("Submission ID is required", { status: 400 });
  }

  const { data: submission } = await managementGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Response("Submission not found", { status: 404 });
  }

  const submissionData = getUnflattenedDataSet(submission);
  const entityDetails = {
    legalEntityName: submission.legalEntityName || "No entity name",
    status: submission.status,
    companyIdentityCode: submission.legalEntityCode,
    masterClientCode: submission.masterClientCode,
    submittedAt: submission.submittedAt,
  }

  return {
    submissionData,
    entityDetails,
  };
}, { authorize: ["es.bahamas.submissions.view"] })

export default function EconomicSubstanceReportSummary(): ReactNode {
  const { submissionData } = useLoaderData<typeof loader>()
  const { relevantActivities } = submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType
  const selectedRelevantActivities = relevantActivities?.filter(activities => activities.selected === "true")
  const taxPayerIdentification = submissionData[Pages.TAX_PAYER_IDENTIFICATION] as TaxPayerIdentificationSchemaType
  const showRelevantActivities = !(taxPayerIdentification?.isBahamianResident === "true" && !!taxPayerIdentification?.files_BusinessLicense)
    && !(taxPayerIdentification?.isBahamianResident === "false" && taxPayerIdentification?.isInvestmentFund === "true")

  return (
    <>
      <SummaryPage>
        <div className="flex flex-col">
          <h1 className="font-inter text-xl tracking-widest text-blue-500 uppercase font-semibold">
            Economic substance reporting
          </h1>
        </div>
        <EntityDetails />
        <FinancialPeriod />
      </SummaryPage>
      <SummaryPage>
        <TaxIdentificationAddress />
      </SummaryPage>
      <SummaryPage>
        <RelevantActivities />
        {relevantActivities && relevantActivities[0].selected !== "true" && <TaxResidencyEntityIncome />}
      </SummaryPage>
      {relevantActivities && relevantActivities[0].selected !== "true" && taxPayerIdentification?.isInvestmentFund === "false" && (
        <SummaryPage>
          <ParentEntity />
        </SummaryPage>
      )}
      {showRelevantActivities && selectedRelevantActivities?.map((activity) => {
        if (activity.id !== "none") {
          return (
            <Fragment key={activity.id}>
              {getActivitySummary(activity)}
            </Fragment>
          )
        }

        return null;
      })}
      <SummaryPage>
        <SupportingDetails />
        <Declaration />
      </SummaryPage>
    </>
  )
}
