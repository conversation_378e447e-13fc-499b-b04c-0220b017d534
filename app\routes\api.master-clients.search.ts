import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { getMasterClients } from "~/services/api-generated";

export async function loader({ request }: LoaderFunctionArgs) {
  await middleware(["auth"], request);

  const url = new URL(request.url);
  const searchTerm = url.searchParams.get("searchTerm");
  const pageSize = Number.parseInt(url.searchParams.get("pageSize") || "50");

  if (!searchTerm) {
    return json({ data: [] });
  }

  try {
    const { data: paginatedMasterClients, error } = await getMasterClients({
      headers: await authHeaders(request),
      query: {
        searchTerm,
        pageNumber: 1,
        pageSize,
      },
    });

    if (error) {
      console.error("Error fetching master clients:", error);

      return json({ error: "Failed to fetch master clients" }, { status: 500 });
    }

    const masterClients = paginatedMasterClients?.data || [];
    const options = masterClients.map(masterClient => ({
      value: masterClient.code as string,
      label: masterClient.code as string,
    }));

    return json({ data: options });
  } catch (err) {
    console.error("Error fetching master clients:", err);

    return json({ error: "Failed to fetch master clients" }, { status: 500 });
  }
}
