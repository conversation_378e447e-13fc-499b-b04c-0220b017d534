import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
  SelectItem,
} from "@netpro/design-system";
import { useFetcher } from "@remix-run/react";
import { Repeat } from "lucide-react";
import { useRemixForm } from "remix-hook-form";
import { Form } from "~/components/Form";
import { FormSelect } from "~/components/FormSelect";
import type { MarkSubmissionsAsPaidRequestDTO, SubmissionDTO } from "~/services/api-generated";
import { updatePaymentsStatusSchema, type UpdatePaymentsStatusSchema } from "../../simplified-tax-return/payments/schemas/update-payments-status";

type Props = {
  selectedSubmissions: string[]
  submissions: SubmissionDTO[]
  open: boolean
  onOpenChange: (value: boolean) => void
}

const paymentStatus = ["PAID", "UNPAID"]

export function UpdatePaymentStatusDialog({ open, onOpenChange, submissions, selectedSubmissions }: Props): ReactNode {
  const fetcher = useFetcher()
  const isSubmitting = fetcher.state === "submitting";
  const selectedCompanies = submissions.filter(sub => selectedSubmissions.includes(sub.id || ""))
  const formMethods = useRemixForm<UpdatePaymentsStatusSchema>({
    mode: "onSubmit",
    stringifyAllValues: false,
    defaultValues: {
      isPaid: "PAID",
    },
    resolver: zodResolver(updatePaymentsStatusSchema),
    submitHandlers: {
      onValid: (data) => {
        const { isPaid } = data
        const body: MarkSubmissionsAsPaidRequestDTO = {
          submissionIds: selectedSubmissions,
          isPaid: isPaid === "PAID",
        }
        fetcher.submit({ data: JSON.stringify(body) }, { method: "post" })
        onOpenChange(false)
      },
    },
  });
  const handleOpenChange = (isOpen: boolean): void => {
    if (!isOpen) {
      formMethods.reset()
    }

    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-screen-md">
        <Form formMethods={formMethods} remixFormProps={{ method: "post" }}>
          <DialogHeader className="flex flex-row items-center gap-2 pb-5">
            <Repeat className="text-primary" />
            <DialogTitle>
              Update Payment Status
            </DialogTitle>
          </DialogHeader>
          {`  You are about to update the payment status for the following  ${selectedSubmissions.length > 1 ? `${selectedSubmissions.length} submissions` : "submission"}:`}
          <ul className="list-decimal list-inside text-sm max-h-96 overflow-y-auto mb-3">
            {selectedCompanies.map(company => (
              <li key={`${company.id}-${company.financialPeriodEndsAt}`}>
                <span className="font-bold">
                  {company.legalEntityName}
                </span>
                {" "}
                -
                {" "}
                {company.financialPeriodEndsAt.split("-")[0]}
              </li>
            ))}
          </ul>
          <FormSelect
            name="isPaid"
            label="New Payment Status*"
            selectValueProps={{ placeholder: "Select payment status" }}
            options={paymentStatus.map(ps => <SelectItem key={ps} value={ps}>{ps}</SelectItem>)}
          />
          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              Update Changes
            </Button>
          </DialogFooter>

        </Form>
      </DialogContent>
    </Dialog>
  );
}
