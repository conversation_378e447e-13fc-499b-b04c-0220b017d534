import type { CompleteRFISchema } from "../../schemas/complete-rfi-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@netpro/design-system";
import { useFetcher, useParams } from "@remix-run/react";
import { Info } from "lucide-react";
import { type ReactNode, useEffect, useState } from "react";
import { useRemixForm } from "remix-hook-form";
import { Form } from "~/components/Form";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormTextarea } from "~/components/FormTextarea";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import type { CompleteRequestForInformationManagementDTO } from "~/services/api-generated";
import { completeRFISchema } from "../../schemas/complete-rfi-schema";

export function CompleteRFIDialog({ routeName }: { routeName: string }): ReactNode {
  const navigate = usePreserveQueryNavigate();
  const fetcher = useFetcher()
  const { id } = useParams();
  const [isOpen, setIsOpen] = useState(false)
  const isSubmitting = fetcher.state === "submitting";
  const formMethods = useRemixForm<CompleteRFISchema>({
    mode: "onSubmit",
    stringifyAllValues: false,
    defaultValues: {
      submittedToRegulator: undefined,
      remark: "",
    },
    resolver: zodResolver(completeRFISchema),
    submitHandlers: {
      onValid: (data) => {
        const { submittedToRegulator, remark } = data
        const body: CompleteRequestForInformationManagementDTO = {
          submittedToRegulator,
          remark,
        }
        fetcher.submit({ data: JSON.stringify(body) }, { method: "post" })
        setIsOpen(false)
      },
    },
  });
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    formMethods.reset()
    if (!open) {
      navigate(`/${routeName}/${id}`)
    }
  }

  useEffect(() => {
    setIsOpen(true)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-screen-md">
        <Form formMethods={formMethods} remixFormProps={{ method: "post" }}>
          <DialogHeader className="flex flex-row items-center gap-2 pb-5">
            <Info className="text-primary" />
            <DialogTitle>
              Mark as Complete
            </DialogTitle>
          </DialogHeader>
          <p className="mb-4 text-sm text-gray-700">Please enter the details to mark it as Complete.</p>
          <FormDatePicker
            name="submittedToRegulator"
            label="Date information submitted to regulator *"
            datePickerProps={{
              disabled: isSubmitting,
              disabledDates: { after: new Date() },
            } as any}
            formItemProps={{ className: "w-2/3" }}
          />
          <FormTextarea
            name="remark"
            label="Add a remark*"
            textareaProps={{ disabled: isSubmitting }}
          />
          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              Confirm
            </Button>
          </DialogFooter>

        </Form>
      </DialogContent>
    </Dialog>
  );
}
