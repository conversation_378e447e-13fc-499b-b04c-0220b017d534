import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON><PERSON>rigger, notify, <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollBar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Form, Link, Outlet, useFetcher, useLoaderData, useNavigation, useRevalidator } from "@remix-run/react";
import { ArrowDownToLine, Check, ClipboardCheck, ListCollapse, OctagonX, Paintbrush, Play, X } from "lucide-react";
import { type ReactNode, useEffect, useState } from "react";
import * as AlertDialog from "~/components/AlertDialog";
import { Button } from "~/components/Button";
import { CardContainer } from "~/components/CardContainer";
import { useMigrationErrors } from "~/features/data-migration/hooks/useMigrationErrors";
import { MigrationStatusCard } from "~/features/data-migration/MigrationStatusCard";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useDelayedState } from "~/lib/hooks/useDelayedState";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { shortMonthFormatWithTime } from "~/lib/utilities/format";
import type { StartMigrationError, StopMigrationError } from "~/services/api-generated";
import { getMigrationStatus, startMigration, stopMigration } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Data Migration",
    to: "/data-migration",
  },
  title: "Data Migration",
}

const Action = {
  Start: "Start",
  Stop: "Stop",
};

export const action = makeEnhancedAction(async ({ request, json }) => {
  await middleware(["auth"], request);

  const formData = await request.formData();

  if (formData.get("_action") === Action.Start) {
    const { error } = await startMigration({ headers: await authHeaders(request) });
    if (error) {
      return json({
        success: false,
        action: "starting",
        error,
      });
    }
  } else if (formData.get("_action") === Action.Stop) {
    const { error } = await stopMigration({ headers: await authHeaders(request) });
    if (error) {
      return json({
        success: false,
        action: "stopping",
        error,
      });
    }
  } else {
    return json({
      success: false,
      error: "Invalid action",
    });
  }

  return json({
    success: true,
  });
}, { authorize: ["str.data-migration"] })

export const loader = makeEnhancedLoader(async ({ request, json }) => {
  await middleware(["auth"], request);
  const response = await getMigrationStatus({ headers: await authHeaders(request) });

  return json({
    migrationStatus: response.data,
  });
}, { authorize: ["str.data-migration"] })

const cleanupAction = "/data-migration/clean-up"

export default function DataMigrationLayout(): ReactNode {
  const formatDate = useFormatDate()
  const navigation = useNavigation()
  const { migrationStatus } = useLoaderData<typeof loader>();
  const { revalidate } = useRevalidator();
  const [isOpen, setIsOpen] = useState(false);
  const migrationErrors = useMigrationErrors(migrationStatus?.unprocessedRecords || []);
  const fetcher = useFetcher<{
    success?: boolean
    action?: string
    error?: StopMigrationError | StartMigrationError
  }>();
  const isSubmitting = useDelayedState(fetcher.state === "submitting", 800);

  useEffect(() => {
    const id = setInterval(revalidate, 1000);

    return () => clearInterval(id);
  }, [revalidate]);

  const onSubmit = (action: string) => {
    fetcher.submit({ _action: action }, {
      action: "/data-migration",
      method: "post",
    })
  }

  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      if (fetcher.data.success) {
        revalidate();
      } else {
        notify({ title: "Error", message: fetcher.data.error?.exceptionMessage as string || `Something went wrong ${fetcher.data.action} the migration.`, variant: "error" });
      }
    }
  }, [fetcher.data, fetcher.state, revalidate]);

  const cleanupLoading = ["loading", "submitting"].includes(navigation.state) && navigation.formAction === cleanupAction

  return (
    <CardContainer>
      <Outlet />
      <div className="space-y-6">
        {migrationStatus?.status === "InProgress" && (
          <Alert variant="info" title="Migration in progress" dismissible>
            One or more data migrations are currently in progress. You don't have to stay on this page for the migration to finish.
          </Alert>
        )}
        {migrationStatus?.error && (
          <Alert variant="error" title="Migration error" dismissible>
            {migrationStatus.error}
          </Alert>
        )}
        <div>
          <div className="flex justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-semibold text-gray-800">Bahamas</h1>
              {!migrationStatus && (
                <Badge variant="success">Ready to migrate</Badge>
              )}
              {migrationStatus?.status === "InProgress" && (
                <Badge variant="info">In Progress</Badge>
              )}
              {migrationStatus?.status === "NotStarted" && (
                <Badge variant="info">Not Started</Badge>
              )}
              {migrationStatus?.status === "Cancelled" && (
                <Badge variant="destructive">Cancelled</Badge>
              )}
              {migrationStatus?.status === "Failed" && (
                <Badge variant="destructive">Failed</Badge>
              )}
              {migrationStatus?.status === "Completed" && (
                <Badge variant="success">Completed</Badge>
              )}
            </div>
            <div className="flex gap-x-2">
              <Link reloadDocument to="./download">
                <Button
                  size="sm"
                  className="flex gap-1.5"
                >
                  Download
                  <ArrowDownToLine size={16} />
                </Button>
              </Link>
              {(migrationStatus?.status === "InProgress" || migrationStatus?.status === "NotStarted")
                ? (
                    <Button
                      size="sm"
                      className="flex gap-1.5"
                      type="button"
                      variant="destructive"
                      isLoading={isSubmitting}
                      onClick={() => onSubmit(Action.Stop)}
                    >
                      Stop migration
                      <OctagonX size={16} />
                    </Button>
                  )
                : (
                    <Button
                      size="sm"
                      className="flex gap-1.5"
                      type="button"
                      isLoading={isSubmitting}
                      onClick={() => onSubmit(Action.Start)}
                    >
                      Start data migration
                      <Play size={16} />
                    </Button>
                  )}
              <AlertDialog.Root open={isOpen} onOpenChange={setIsOpen}>
                <AlertDialog.Trigger>
                  <Button
                    size="sm"
                    className="flex gap-1.5"
                    type="button"
                    onClick={() => setIsOpen(true)}
                  >
                    Run post-migration cleanup
                    <Paintbrush size={16} />
                  </Button>
                </AlertDialog.Trigger>
                <AlertDialog.Portal>
                  <AlertDialog.Overlay />
                  <AlertDialog.Content>
                    <AlertDialog.Title>
                      Are you sure?
                    </AlertDialog.Title>
                    <AlertDialog.Description>
                      Running this cleanup will delete all closed companies which do not have any submissions. This should only be done after a successful data migration. This action is irreversible, are you sure you want to continue?
                    </AlertDialog.Description>
                    <AlertDialog.Footer>
                      <AlertDialog.Cancel asChild>
                        <Button onClick={() => setIsOpen(false)} variant="outline" size="sm" disabled={cleanupLoading}>
                          Cancel
                        </Button>
                      </AlertDialog.Cancel>
                      <Form method="post" action={cleanupAction}>
                        <Button
                          size="sm"
                          variant="destructive"
                          className="flex gap-1.5"
                          type="submit"
                          isLoading={cleanupLoading}
                        >
                          Confirm
                        </Button>
                      </Form>
                    </AlertDialog.Footer>
                  </AlertDialog.Content>
                </AlertDialog.Portal>
              </AlertDialog.Root>
            </div>
          </div>
          {migrationStatus?.lastUpdated && (
            <span className="text-sm text-gray-500">
              {`Last updated: ${formatDate(migrationStatus.lastUpdated, { formatStr: shortMonthFormatWithTime })}`}
            </span>
          )}
        </div>
        <div>
          {migrationStatus?.entityMigrationProgresses && migrationStatus?.entityMigrationProgresses.map(entity => (
            <div key={entity.entityName}>
              <h1 className="text-lg font-semibold text-gray-500">{entity.entityName}</h1>
              <div className="flex space-x-3 py-3">
                <MigrationStatusCard icon={ListCollapse} count={entity.sourceCount || 0} entityName="Total records" />
                <MigrationStatusCard icon={ClipboardCheck} count={entity.processedCount || 0} entityName="Processed" />
                <MigrationStatusCard icon={Check} count={entity.successCount || 0} entityName="Migrated" />
                <Dialog modal>
                  <DialogTrigger disabled={entity.failedCount === 0}>
                    <MigrationStatusCard icon={X} count={entity.failedCount || 0} entityName="Failed records" />
                  </DialogTrigger>
                  <DialogContent className="max-w-screen-xl">
                    <ScrollArea className="max-h-[80vh]">
                      <DialogHeader className="font-semibold text-lg pb-5">Failed records</DialogHeader>
                      {migrationErrors(entity.entityName).length > 0
                        ? (
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Identifier</TableHead>
                                  <TableHead>Reason</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {migrationErrors(entity.entityName).map(record => (
                                  <TableRow key={record.identifier}>
                                    <TableCell>{record.identifier}</TableCell>
                                    <TableCell>{record.reason}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          )
                        : (
                            <Alert variant="info" title="No failed records" />
                          )}
                      <ScrollBar />
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          ))}
        </div>
      </div>
    </CardContainer>
  );
}
