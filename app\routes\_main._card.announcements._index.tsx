import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { Filter, Plus } from "lucide-react";
import { z } from "zod";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormSearch } from "~/components/FormSearch";
import { useAnnouncementColumns } from "~/features/announcements/hooks/use-announcements-columns";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementListAnnouncements } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Announcements",
    to: "/announcements",
  },
  title: "Announcements",
}

export const loader = makeEnhancedLoader(async ({ json, request, getUserPreferences, enhancedURL }) => {
  const { tablePageSize } = await getUserPreferences()
  await middleware(["auth"], request);
  const { data: announcementsData, error: announcementsError } = await managementListAnnouncements({ headers: await authHeaders(request), query: {
    PageNumber: Number(enhancedURL.searchParams.get("page")) || undefined,
    GeneralSearchTerm: enhancedURL.searchParams.get("search") ?? undefined,
    SortOrder: enhancedURL.searchParams.get("orderDirection") ?? undefined,
    SortBy: enhancedURL.searchParams.get("order") as any ?? undefined,
    PageSize: tablePageSize,
  } })

  if (announcementsError) {
    throw new Response("Failed to fetch announcements", { status: 500 })
  }

  return json({
    announcementsData,
  })
}, {
  authorize: ["announcements.view", "announcements.search"],
  featureEnabled: ["Announcements"],
})

const searchSchema = z.object({
  search: z.string().optional(),
})

export default function Announcements(): ReactNode {
  const navigate = useNavigate();
  const { announcementsData } = useLoaderData<typeof loader>()
  const { formMethods } = useFilterForm(searchSchema);
  const { columns } = useAnnouncementColumns();

  return (
    <CardContainer>
      <Authorized oneOf={["announcements.search"]}>
        <Form formMethods={formMethods}>
          <Authorized oneOf={["announcements.create"]}>
            <div className="flex justify-end pb-2">
              <Button
                type="button"
                className="w-fit"
                onClick={() => navigate("/announcements/new")}
              >
                <Plus className="size-5 mr-2" />
                New Announcement
              </Button>
            </div>
          </Authorized>
          <FilterRow>
            <div className="col-span-full flex flex-row items-center gap-2">
              <FormSearch
                name="search"
                formItemProps={{ className: "w-full" }}
                inputProps={{ placeholder: "Search on subject" }}
              />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
            </div>
          </FilterRow>
        </Form>
      </Authorized>
      <EnhancedTableContainer>
        <EnhancedTable
          rowId="id"
          columns={columns}
          data={announcementsData?.data}
          totalItems={announcementsData?.totalItemCount}
          onRowClick={row => navigate(`/announcements/${row.id}`)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  )
}
