import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogD<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON>alog<PERSON>eader, Dialog<PERSON>itle, DialogTrigger } from "@netpro/design-system";
import { Form, useNavigate, useParams } from "@remix-run/react";
import { X } from "lucide-react";
import { useState } from "react";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementAnnouncementGetById, managementDeleteAnnouncement } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ json, request, params }) => {
  await middleware(["auth"], request);
  const { id } = params
  if (!id) {
    throw new Response("The Id is required to fetch the announcement data", { status: 400 })
  }

  const { data: announcement } = await managementAnnouncementGetById({ headers: await authHeaders(request), path: { announcementId: id } })

  if (announcement?.sentAt) {
    throw new Response("Changes cannot be made because the announcement has already been sent.", { status: 403 });
  }

  return json(null)
}, {
  authorize: ["announcements.delete"],
  featureEnabled: ["Announcements"],
})

export const action = makeEnhancedAction(async ({ params, request, redirect, setNotification }) => {
  await middleware(["auth"], request);
  const { id } = params
  if (!id) {
    throw new Response("The Id is required to fetch the announcement data", { status: 400 })
  }

  await managementDeleteAnnouncement({ headers: await authHeaders(request), path: { announcementId: id } })
  setNotification({ title: "Announcement deleted successfully", variant: "success" })

  return redirect("/announcements")
}, {
  authorize: ["announcements.delete"],
  featureEnabled: ["Announcements"],
});

export type EditAnnouncementLoaderData = typeof loader;

export default function AnnouncementDelete() {
  const [open, setOpen] = useState(true)
  const navigate = useNavigate()
  const { id } = useParams()
  const handleClose = () => {
    setOpen(false)
    navigate(`/announcements/${id}`)
  }

  return (
    <div className="absolute">
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogTrigger asChild>
          <Button size="sm" variant="outline" name="intent" value="removeUser" className="h-6 px-2 group inline-flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
            <X size={12} className="text-destructive" />
            <span className="text-xs">Remove</span>
          </Button>
        </DialogTrigger>
        <DialogContent>
          <Form method="post">
            <DialogHeader>
              <DialogTitle>Are you sure?</DialogTitle>
              <DialogDescription>
                Clicking 'Confirm' will delete the announcement
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>Cancel</Button>
              <Button type="submit" variant="destructive">Confirm</Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
