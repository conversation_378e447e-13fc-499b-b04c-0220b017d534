import type { ReactNode } from "react";
import { CancelRFIDialog } from "~/features/rfi/components/dialogs/CancelRFIDialog";
import { getRfiCancelAction } from "~/features/rfi/utlities/rfi-cancel-action.server";
import { getRfiLoader } from "~/features/rfi/utlities/rfi-cancel-loader.server";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";

const routeName = "simplified-tax-return/submissions"
export const loader = makeEnhancedLoader(async (args) => {
  try {
    const result = await getRfiLoader(routeName, args);

    return result ?? {};
  } catch (err) {
    if (err instanceof Response && err.status === 404) {
      return {};
    }

    throw err;
  }
}, {
  authorize: ["rfi.start", "rfi.cancel"],
});

export const action = makeEnhancedAction(async (args) => {
  return getRfiCancelAction(routeName, args)
}, {
  authorize: ["rfi.start", "rfi.cancel"],
});

export default function SimplifiedTaxReturnRfiCancel(): ReactNode {
  return (<CancelRFIDialog routeName={routeName} />)
}
