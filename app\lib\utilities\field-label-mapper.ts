import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

export type BoDirEntity = BeneficialOwnerDTO | DirectorDTO;
export type BoDirFieldName = keyof BeneficialOwnerDTO | keyof DirectorDTO;
export type FieldLabelConfig = {
  /** Default label for the field */
  defaultLabel: string
  /** Conditional overrides based on entity properties */
  conditions?: {
    /** Override when isIndividual is true */
    individual?: string
    /** Override when isIndividual is false */
    corporate?: string
    /** Override based on officerTypeCode values */
    officerTypeCodes?: Record<string, string>
  }
}

/**
 * Base field label mappings with default labels and conditional overrides
 */
const FIELD_LABEL_MAPPINGS: Record<string, FieldLabelConfig> = {
  // Name field - varies significantly based on entity type
  name: {
    defaultLabel: "Full Name",
    conditions: {
      individual: "Full Name",
      corporate: "Corporation Name", // For beneficial owners
    },
  },

  // Officer type fields
  officerTypeName: {
    defaultLabel: "Type of Officer",
    conditions: {
      // For beneficial owners, it's "Type of Owner" when individual
      individual: "Type of Owner", // This will be overridden by entity-specific logic
      corporate: "Type",
    },
  },

  // Personal information fields (individual only)
  dateOfBirth: {
    defaultLabel: "Date of Birth",
  },
  countryOfBirth: {
    defaultLabel: "Country of Birth",
    conditions: {
      // Directors use "Place of Birth" for individuals
      individual: "Place of Birth", // This will be overridden by entity-specific logic
    },
  },
  placeOfBirth: {
    defaultLabel: "Place of Birth",
  },
  nationality: {
    defaultLabel: "Nationality",
  },

  // Corporate information fields
  incorporationNumber: {
    defaultLabel: "Incorporation Number",
  },
  dateOfIncorporation: {
    defaultLabel: "Incorporation Date",
  },
  countryOfFormation: {
    defaultLabel: "Country of Formation",
  },
  incorporationCountry: {
    defaultLabel: "Country of Formation",
  },

  // Address fields
  residentialAddress: {
    defaultLabel: "Residential Address",
  },
  address: {
    defaultLabel: "Address",
    conditions: {
      corporate: "Registered Office Address", // For corporate directors
    },
  },
  serviceAddress: {
    defaultLabel: "Service Address",
  },

  // Date fields
  appointmentDate: {
    defaultLabel: "Appointment Date",
  },
  cessationDate: {
    defaultLabel: "Cessation Date",
  },

  // Tax and regulatory fields
  tin: {
    defaultLabel: "TIN",
  },

  // Stock exchange fields (specific to certain officer types)
  stockExchange: {
    defaultLabel: "Stock Exchange Name",
  },
  stockCode: {
    defaultLabel: "Stock Listing ID",
  },

  // Other fields
  formerName: {
    defaultLabel: "Former Name",
  },
  directorType: {
    defaultLabel: "Director Type",
  },
  jurisdictionOfRegulator: {
    defaultLabel: "Jurisdiction of Regulator",
  },
  nameOfRegulator: {
    defaultLabel: "Name of Regulator",
  },
  sovereignState: {
    defaultLabel: "Sovereign State",
  },
};

/**
 * Determines if an entity is a beneficial owner based on available properties
 */
function isBeneficialOwner(entity: BoDirEntity): entity is BeneficialOwnerDTO {
  return "officerTypeCode" in entity;
}

/**
 * Determines if an entity is a director based on available properties
 */
function isDirector(entity: BoDirEntity): entity is DirectorDTO {
  return "directorType" in entity;
}

/**
 * Maps a field name to a human-readable label based on the entity and its properties
 *
 * @param entity - The beneficial owner or director entity
 * @param fieldName - The field name to map
 * @returns The human-readable label for the field
 */
export function mapFieldToLabel(entity: BoDirEntity, fieldName: string): string {
  const config = FIELD_LABEL_MAPPINGS[fieldName];

  if (!config) {
    // If no mapping exists, return the field name as-is (fallback)
    return fieldName;
  }

  let label = config.defaultLabel;

  // Apply conditional overrides
  if (config.conditions) {
    // Check individual vs corporate
    if (entity.isIndividual && config.conditions.individual) {
      label = config.conditions.individual;
    } else if (!entity.isIndividual && config.conditions.corporate) {
      label = config.conditions.corporate;
    }

    // Apply entity-specific overrides
    if (isBeneficialOwner(entity)) {
      label = applyBeneficialOwnerOverrides(entity, fieldName, label, config);
    } else if (isDirector(entity)) {
      label = applyDirectorOverrides(entity, fieldName, label, config);
    }

    // Check officerTypeCode conditions
    if (config.conditions.officerTypeCodes && entity.officerTypeCode) {
      const officerTypeOverride = config.conditions.officerTypeCodes[entity.officerTypeCode];
      if (officerTypeOverride) {
        label = officerTypeOverride;
      }
    }
  }

  return label;
}

/**
 * Apply beneficial owner specific label overrides
 */
function applyBeneficialOwnerOverrides(
  entity: BeneficialOwnerDTO,
  fieldName: string,
  currentLabel: string,
  config: FieldLabelConfig,
): string {
  // Beneficial owner specific logic
  switch (fieldName) {
    case "name":
      if (!entity.isIndividual) {
        return "Corporation Name";
      }

      break;
    case "officerTypeName":
      if (entity.isIndividual) {
        return "Type of Owner";
      } else {
        return "Type";
      }

    case "countryOfBirth":
      // Beneficial owners use "Country of Birth" for individuals
      return "Country of Birth";
  }

  return currentLabel;
}

/**
 * Apply director specific label overrides
 */
function applyDirectorOverrides(
  entity: DirectorDTO,
  fieldName: string,
  currentLabel: string,
  config: FieldLabelConfig,
): string {
  // Director specific logic
  switch (fieldName) {
    case "name":
      if (!entity.isIndividual) {
        return "Corporate Name";
      }

      break;
    case "officerTypeName":
      return "Type of Officer";
    case "countryOfBirth":
      if (entity.isIndividual) {
        return "Place of Birth";
      }

      break;
    case "address":
      if (!entity.isIndividual) {
        return "Registered Office Address";
      }

      break;
  }

  return currentLabel;
}

/**
 * Utility function to check if a field should be visible based on conditions
 * This can be extended in the future for more complex visibility logic
 */
export function isFieldVisible(entity: BoDirEntity, fieldName: string): boolean {
  /*
   * For now, return true for all fields
   * This can be extended to include visibility logic based on:
   * - entity.isIndividual
   * - entity.officerTypeCode
   * - other business rules
   */
  return true;
}
