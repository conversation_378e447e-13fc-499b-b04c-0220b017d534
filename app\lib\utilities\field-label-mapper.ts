import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

export type BoDirEntity = BeneficialOwnerDTO | DirectorDTO;
export type BoDirFieldName = keyof BeneficialOwnerDTO | keyof DirectorDTO;

export type ConditionFunction = (entity: BoDirEntity) => boolean;
export type ConditionRule = {
  condition: ConditionFunction
  label: string
};

export type FieldLabelConfig = {
  defaultLabel: string
  conditions?: ConditionRule[]
};
export type FieldMapping = string | FieldLabelConfig;

/**
 * Helper functions for common conditions
 * When new conditions are needed, add them here (new jurisdictions, new entity types, etc.)
 */
const isIndividual = (entity: BoDirEntity): boolean => entity.isIndividual === true;
const isCorporate = (entity: BoDirEntity): boolean => entity.isIndividual === false;
const isBeneficialOwner = (entity: BoDirEntity): entity is BeneficialOwnerDTO => "officerTypeCode" in entity;
const isDirector = (entity: BoDirEntity): entity is DirectorDTO => "directorType" in entity;
const isIndividualBeneficialOwner = (entity: BoDirEntity): boolean => isIndividual(entity) && isBeneficialOwner(entity);
const isCorporateBeneficialOwner = (entity: BoDirEntity): boolean => isCorporate(entity) && isBeneficialOwner(entity);
const isIndividualDirector = (entity: BoDirEntity): boolean => isIndividual(entity) && isDirector(entity);
const isCorporateDirector = (entity: BoDirEntity): boolean => isCorporate(entity) && isDirector(entity);
/**
 * Base field label mappings with default labels and conditional overrides
 * Simple format: field: "Label" - for fields without conditions
 * Complex format: field: { defaultLabel: "Label", conditions: [...] } - for fields with conditions
 */
const FIELD_LABEL_MAPPINGS: Record<string, FieldMapping> = {
  // Fields with complex conditions
  name: {
    defaultLabel: "Full Name",
    conditions: [
      { condition: isCorporateBeneficialOwner, label: "Corporation Name" },
      { condition: isCorporateDirector, label: "Corporate Name" },
      { condition: isIndividual, label: "Full Name" },
    ],
  },
  officerTypeName: {
    defaultLabel: "Type of Officer",
    conditions: [
      { condition: isIndividualBeneficialOwner, label: "Type of Owner" },
      { condition: isCorporateBeneficialOwner, label: "Type" },
      { condition: isDirector, label: "Type of Officer" },
    ],
  },
  countryOfBirth: {
    defaultLabel: "Country of Birth",
    conditions: [
      { condition: isIndividualDirector, label: "Place of Birth" },
      { condition: isBeneficialOwner, label: "Country of Birth" },
    ],
  },
  address: {
    defaultLabel: "Address",
    conditions: [
      { condition: isCorporateDirector, label: "Registered Office Address" },
    ],
  },
  // Simple fields without conditions
  dateOfBirth: "Date of Birth",
  placeOfBirth: "Place of Birth",
  nationality: "Nationality",
  incorporationNumber: "Incorporation Number",
  dateOfIncorporation: "Incorporation Date",
  countryOfFormation: "Country of Formation",
  incorporationCountry: "Country of Formation",
  residentialAddress: "Residential Address",
  serviceAddress: "Service Address",
  appointmentDate: "Appointment Date",
  cessationDate: "Cessation Date",
  tin: "TIN",
  stockExchange: "Stock Exchange Name",
  stockCode: "Stock Listing ID",
  formerName: "Former Name",
  directorType: "Director Type",
  jurisdictionOfRegulator: "Jurisdiction of Regulator",
  nameOfRegulator: "Name of Regulator",
  sovereignState: "Sovereign State",
};

/**
 * Maps a field name to a human-readable label based on the entity and its properties
 *
 * @param entity - The beneficial owner or director entity
 * @param fieldName - The field name to map
 * @returns The human-readable label for the field
 */
export function mapFieldToLabel(entity: BoDirEntity, fieldName: string): string {
  const mapping = FIELD_LABEL_MAPPINGS[fieldName];

  if (!mapping) {
    // If no mapping exists, return the field name as-is (fallback)
    return fieldName;
  }

  // Handle simple string mapping
  if (typeof mapping === "string") {
    return mapping;
  }

  // Handle complex object mapping with conditions
  let label = mapping.defaultLabel;

  // Apply condition rules - first match wins
  if (mapping.conditions) {
    for (const rule of mapping.conditions) {
      if (rule.condition(entity)) {
        label = rule.label;
        break; // First match wins
      }
    }
  }

  return label;
}
