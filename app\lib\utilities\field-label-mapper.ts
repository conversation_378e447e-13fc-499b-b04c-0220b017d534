import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

export type BoDirEntity = BeneficialOwnerDTO | DirectorDTO;
export type BoDirFieldName = keyof BeneficialOwnerDTO | keyof DirectorDTO;

/**
 * Condition function that takes an entity and returns true if the condition matches
 */
export type ConditionFunction = (entity: BoDirEntity) => boolean;

/**
 * Condition rule that pairs a condition function with a label
 */
export type ConditionRule = {
  condition: ConditionFunction
  label: string
};

/**
 * Field label configuration with generic condition system
 */
export type FieldLabelConfig = {
  /** Default label for the field */
  defaultLabel: string
  /** Array of condition rules, evaluated in order. First match wins. */
  conditions?: ConditionRule[]
};

/**
 * Helper functions for common conditions
 * When new conditions are needed, add them here (new jurisdictions, new en)
 */
const isIndividual = (entity: BoDirEntity): boolean => entity.isIndividual === true;
const isCorporate = (entity: BoDirEntity): boolean => entity.isIndividual === false;
const isBeneficialOwner = (entity: BoDirEntity): entity is BeneficialOwnerDTO => "officerTypeCode" in entity;
const isDirector = (entity: BoDirEntity): entity is DirectorDTO => "directorType" in entity;
const isIndividualBeneficialOwner = (entity: BoDirEntity): boolean => isIndividual(entity) && isBeneficialOwner(entity);
const isCorporateBeneficialOwner = (entity: BoDirEntity): boolean => isCorporate(entity) && isBeneficialOwner(entity);
const isIndividualDirector = (entity: BoDirEntity): boolean => isIndividual(entity) && isDirector(entity);
const isCorporateDirector = (entity: BoDirEntity): boolean => isCorporate(entity) && isDirector(entity);
/**
 * Base field label mappings with default labels and conditional overrides
 */
const FIELD_LABEL_MAPPINGS: Record<string, FieldLabelConfig> = {
  // Name field - varies significantly based on entity type
  name: {
    defaultLabel: "Full Name",
    conditions: [
      { condition: isCorporateBeneficialOwner, label: "Corporation Name" },
      { condition: isCorporateDirector, label: "Corporate Name" },
      { condition: isIndividual, label: "Full Name" },
    ],
  },

  // Officer type fields
  officerTypeName: {
    defaultLabel: "Type of Officer",
    conditions: [
      { condition: isIndividualBeneficialOwner, label: "Type of Owner" },
      { condition: isCorporateBeneficialOwner, label: "Type" },
      { condition: isDirector, label: "Type of Officer" },
    ],
  },

  // Personal information fields (individual only)
  dateOfBirth: {
    defaultLabel: "Date of Birth",
  },
  countryOfBirth: {
    defaultLabel: "Country of Birth",
    conditions: [
      { condition: isIndividualDirector, label: "Place of Birth" },
      { condition: isBeneficialOwner, label: "Country of Birth" },
    ],
  },
  placeOfBirth: {
    defaultLabel: "Place of Birth",
  },
  nationality: {
    defaultLabel: "Nationality",
  },

  // Corporate information fields
  incorporationNumber: {
    defaultLabel: "Incorporation Number",
  },
  dateOfIncorporation: {
    defaultLabel: "Incorporation Date",
  },
  countryOfFormation: {
    defaultLabel: "Country of Formation",
  },
  incorporationCountry: {
    defaultLabel: "Country of Formation",
  },
  residentialAddress: {
    defaultLabel: "Residential Address",
  },
  address: {
    defaultLabel: "Address",
    conditions: [
      { condition: isCorporateDirector, label: "Registered Office Address" },
    ],
  },
  serviceAddress: {
    defaultLabel: "Service Address",
  },

  // Date fields
  appointmentDate: {
    defaultLabel: "Appointment Date",
  },
  cessationDate: {
    defaultLabel: "Cessation Date",
  },

  // Tax and regulatory fields
  tin: {
    defaultLabel: "TIN",
  },

  stockExchange: {
    defaultLabel: "Stock Exchange Name",
  },
  stockCode: {
    defaultLabel: "Stock Listing ID",
  },

  // Other fields
  formerName: {
    defaultLabel: "Former Name",
  },
  directorType: {
    defaultLabel: "Director Type",
  },
  jurisdictionOfRegulator: {
    defaultLabel: "Jurisdiction of Regulator",
  },
  nameOfRegulator: {
    defaultLabel: "Name of Regulator",
  },
  sovereignState: {
    defaultLabel: "Sovereign State",
  },
};

/**
 * Maps a field name to a human-readable label based on the entity and its properties
 *
 * @param entity - The beneficial owner or director entity
 * @param fieldName - The field name to map
 * @returns The human-readable label for the field
 */
export function mapFieldToLabel(entity: BoDirEntity, fieldName: string): string {
  const config = FIELD_LABEL_MAPPINGS[fieldName];

  if (!config) {
    // If no mapping exists, return the field name as-is (fallback)
    return fieldName;
  }

  // Start with the default label
  let label = config.defaultLabel;

  // Apply condition rules - first match wins
  if (config.conditions) {
    for (const rule of config.conditions) {
      if (rule.condition(entity)) {
        label = rule.label;
        break; // First match wins
      }
    }
  }

  return label;
}

/**
 * Utility function to check if a field should be visible based on conditions
 * This can be extended in the future for more complex visibility logic
 */
export function isFieldVisible(_entity: BoDirEntity, _fieldName: string): boolean {
  /*
   * For now, return true for all fields
   * This can be extended to include visibility logic based on:
   * - entity.isIndividual
   * - entity.officerTypeCode
   * - other business rules
   */
  return true;
}

/**
 * Helper function to create condition for specific officer type codes
 */
export function hasOfficerTypeCode(code: string) {
  return (entity: BoDirEntity): boolean => {
    return isBeneficialOwner(entity) && entity.officerTypeCode === code;
  }
}

/**
 * Helper function to create condition for multiple officer type codes
 */
export function hasAnyOfficerTypeCode(codes: string[]) {
  return (entity: BoDirEntity): boolean => {
    return isBeneficialOwner(entity) && codes.includes(entity.officerTypeCode || "");
  }
}
