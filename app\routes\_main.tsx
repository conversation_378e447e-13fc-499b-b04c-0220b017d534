import type { JSX } from "react";
import { Outlet, useLoaderData } from "@remix-run/react";
import { Sidebar } from "~/components/layout/sidebar/Sidebar";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { FeatureFlagProvider } from "~/lib/feature-flags/context/feature-flags-context";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { commonGetFeatureFlags } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, json }) => {
  await middleware(["auth"], request);

  const { data: featureFlags } = await commonGetFeatureFlags({
    headers: await authHeaders(request),
  });

  return json({
    featureFlags,
  });
});

export default function MainLayout(): JSX.Element {
  const { featureFlags } = useLoaderData<typeof loader>();

  return (
    <FeatureFlagProvider initialFlags={{ featureFlags: featureFlags || [] }}>
      <Sidebar>
        <Outlet />
      </Sidebar>
    </FeatureFlagProvider>
  );
}
