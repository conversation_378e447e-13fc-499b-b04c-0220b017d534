import type { ReactNode } from "react";
import { RfiForm } from "~/features/rfi/components/forms/RfiForm";
import { getRfiAction } from "~/features/rfi/utlities/rfi-action.server";
import { getRfiLoader } from "~/features/rfi/utlities/rfi-loader.server";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";

const routeName = "simplified-tax-return/submissions"
export const loader = makeEnhancedLoader(async (args) => {
  return getRfiLoader(routeName, args)
}, {
  authorize: ["rfi.start"],
});

export const action = makeEnhancedAction(async (args) => {
  return getRfiAction(routeName, args)
}, {
  authorize: ["rfi.start"],
});

export default function SimplifiedTaxReturnRfi(): ReactNode {
  return (<RfiForm routeName={routeName} />)
}
